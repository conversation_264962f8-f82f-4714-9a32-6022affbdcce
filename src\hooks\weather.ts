import axios from 'axios'
import dayjs from 'dayjs'
import Cookies from 'js-cookie'
import { ref } from 'vue'

const icons = ref<{ [key: string]: string[] }>({
  'clear-day': ['晴'],
  'cloudy': ['少云'],
  'partly-cloudy-day': ['晴间多云'],
  'overcast-day': ['多云'],
  'overcast': ['阴'],
  'wind': ['有风', '平静', '微风', '和风', '清风', '强风/劲风', '疾风', '大风', '烈风'],
  'hurricane': ['风暴', '飓风', '热带风暴'],
  'tornado': ['龙卷风'],
  'haze': ['霾', '中度霾', '重度霾', '严重霾'],
  'drizzle': ['雨', '毛毛雨/细雨', '小雨', '中雨', '大雨', '小雨-中雨', '中雨-大雨'],
  'rain': ['暴雨', '大暴雨', '特大暴雨', '大雨-暴雨', '暴雨-大暴雨', '大暴雨-特大暴雨'],
  'partly-cloudy-day-rain': ['阵雨', '雷阵雨'],
  'partly-cloudy-day-hail': ['雷阵雨并伴有冰雹'],
  'thunderstorms-rain': ['强阵雨', '强雷阵雨', '极端降雨'],
  'sleet': ['雨雪天气', '雨夹雪', '冻雨'],
  'partly-cloudy-day-sleet': ['阵雨夹雪'],
  'snow': ['雪', '小雪', '中雪', '大雪', '小雪-中雪', '中雪-大雪'],
  'partly-cloudy-day-snow': ['阵雪'],
  'thunderstorms-snow': ['暴雪', '大雪-暴雪'],
  'dust': ['浮尘'],
  'dust-wind': ['扬沙', '沙尘暴', '强沙尘暴'],
  'fog': ['雾', '浓雾', '强浓雾', '轻雾', '大雾', '特强浓雾'],
  'thermometer-warmer': ['热'],
  'thermometer-colder': ['冷'],
  'not-available': ['未知'],
})

const key = import.meta.env.VITE_AMAP_SERVICE

// 转换天气图标
function getIcon(code: string) {
  let icon = './weather/not-available.svg'
  Object.keys(icons.value).map((item) => {
    if (icons.value[item].includes(code)) {
      icon = `./weather/${item}.svg`
    }
    return item
  })
  return icon
}

export function useWeather() {
  // 所在地区
  const city = ref(Cookies.get('city') || '')

  // 获取地区
  async function getArea() {
    const res = await axios.request({
      url: 'https://restapi.amap.com/v3/ip',
      params: { key },
    })
    if (res.status === 200) {
      city.value = res.data.area || res.data.city
      if (city.value) {
        Cookies.set('city', city.value, { expires: dayjs().valueOf() + 1000 * 60 * 10 })
      }
    }
  }

  // 获取今天天气
  async function getToday() {
    if (!city.value)
      await getArea()
    if (city.value) {
      const res = await axios.request({
        url: 'https://restapi.amap.com/v3/weather/weatherInfo',
        params: {
          key,
          city: city.value,
          extensions: 'base',
        },
      })
      return res.data.lives[0]
    }
    else {
      return false
    }
  }

  // 获取未来天气
  async function getCasts() {
    if (!city.value)
      await getArea()
    if (city.value) {
      const res = await axios.request({
        url: 'https://restapi.amap.com/v3/weather/weatherInfo',
        params: {
          key,
          city: city.value,
          extensions: 'all',
        },
      })
      return res.data.forecasts[0].casts
    }
    else {
      return false
    }
  }

  return {
    city,
    getArea,
    getToday,
    getCasts,
    getIcon,
  }
}
