<script setup lang="ts">
import emitter from '@/utils/mitt'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'

const props = defineProps<{
  data: any
}>()

const total = computed(() => {
  return props.data?.expert.length
})

const farmNum = computed(() => {
  return props.data?.expert.filter((item: any) => item.class.includes('1')).length
})

const factoryNum = computed(() => {
  return props.data?.expert.filter((item: any) => item.class.includes('2')).length
})

const serviceNumber = computed(() => {
  return props.data?.expert.map((item: any) => item.serviceNumber).reduce((a: number, b: number) => a + b, 0)
})

onMounted(() => {
  new Player({
    id: 'mse',
    url: props.data?.video.startsWith('http') ? props.data?.video : import.meta.env.VITE_STATIC_BASE_URL + props.data?.video,
    poster: import.meta.env.VITE_STATIC_BASE_URL + props.data?.cover[0],
    height: '100%',
    width: '100%',
  })
})
</script>

<template>
  <div class="box">
    <!-- card1 -->
    <div class="card">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">富安茧丝绸产业介绍</span>
      </div>
      <div class="bd m-t-20rem">
        <div class="video h-240rem w-full">
          <div id="mse" />
          <div class="corner-top" />
          <div class="corner-bottom" />
        </div>
        <div class="desc m-t-20rem h-170rem text-14rem">
          {{ data?.intro }}
        </div>
      </div>
    </div>
    <!-- card2 -->
    <div class="card m-t-20rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">专家服务</span>
      </div>
      <div class="bd expert m-t-20rem">
        <div class="item flex cursor-pointer items-center justify-between" @click="emitter.emit('homeExpertTab', 0)">
          <img class="h-56rem w-56rem" src="@/assets/images/<EMAIL>">
          <div class="w-280rem p-l-15rem text-18rem font-bold">
            专家人数
          </div>
          <div class="w-100rem text-right text-14rem">
            <span class="m-r-5rem text-24rem text-#FFAB2B font-ddin">{{ total }}</span>人
          </div>
        </div>
        <div class="item m-t-15rem flex cursor-pointer items-center justify-between" @click="emitter.emit('homeExpertTab', 1)">
          <img class="h-35rem w-35rem" src="@/assets/images/<EMAIL>">
          <div class="w-280rem p-l-15rem text-16rem">
            农业专家
          </div>
          <div class="w-100rem text-right text-14rem">
            <span class="m-r-5rem text-24rem text-#FFAB2B font-ddin">{{ farmNum }}</span>人
          </div>
        </div>
        <div class="item m-t-15rem flex cursor-pointer items-center justify-between" @click="emitter.emit('homeExpertTab', 2)">
          <img class="h-35rem w-35rem" src="@/assets/images/<EMAIL>">
          <div class="w-280rem p-l-15rem text-16rem">
            工业专家
          </div>
          <div class="w-100rem text-right text-14rem">
            <span class="m-r-5rem text-24rem text-#FFAB2B font-ddin">{{ factoryNum }}</span>人
          </div>
        </div>
        <div class="item m-t-15rem flex items-center justify-between">
          <img class="h-35rem w-35rem" src="@/assets/images/<EMAIL>">
          <div class="w-280rem p-l-15rem text-16rem">
            服务人次
          </div>
          <div class="w-100rem text-right text-14rem">
            <span class="m-r-5rem text-24rem text-#FFAB2B font-ddin">{{ serviceNumber }}</span>次
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: 464rem;
  height: 936rem;
  left: 20rem;
  bottom: 20rem;
}
.desc {
  text-indent: 2em;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 3rem;
    background: rgba(37, 220, 167, 0.1);
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(37, 220, 167, 0.25);
    &:hover {
      background: rgba(37, 220, 167, 0.75);
      cursor: pointer;
    }
  }
}
.video {
  position: relative;
  border: 1rem solid #25dca7;
  .corner-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10rem;
    &::before {
      content: '';
      position: absolute;
      top: -1rem;
      left: -1rem;
      width: 10rem;
      height: 10rem;
      background: url('@/assets/images/<EMAIL>') no-repeat;
      background-size: contain;
      transform: rotate(0deg);
    }
    &::after {
      content: '';
      position: absolute;
      top: -1rem;
      right: -1rem;
      width: 10rem;
      height: 10rem;
      background: url('@/assets/images/<EMAIL>') no-repeat;
      background-size: contain;
      transform: rotate(90deg);
    }
  }
  .corner-bottom {
    position: absolute;
    z-index: 99;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 10rem;
    &::before {
      content: '';
      position: absolute;
      bottom: -1rem;
      left: -1rem;
      width: 10rem;
      height: 10rem;
      background: url('@/assets/images/<EMAIL>') no-repeat;
      background-size: contain;
      transform: rotate(270deg);
    }
    &::after {
      content: '';
      position: absolute;
      bottom: -1rem;
      right: -1rem;
      width: 10rem;
      height: 10rem;
      background: url('@/assets/images/<EMAIL>') no-repeat;
      background-size: contain;
      transform: rotate(180deg);
    }
  }
}
.expert {
  .item {
    padding: 10rem 20rem;
    background:
      linear-gradient(#25d4dc, #25d4dc) left top/5rem 1rem no-repeat,
      linear-gradient(#25d4dc, #25d4dc) right top/5rem 1rem no-repeat,
      linear-gradient(#25d4dc, #25d4dc) left bottom/5rem 1rem no-repeat,
      linear-gradient(#25d4dc, #25d4dc) right bottom/5rem 1rem no-repeat,
      rgba(37, 212, 220, 0.16);
    &.cursor-pointer:hover {
      background:
        linear-gradient(#25d4dc, #25d4dc) left top/5rem 1rem no-repeat,
        linear-gradient(#25d4dc, #25d4dc) right top/5rem 1rem no-repeat,
        linear-gradient(#25d4dc, #25d4dc) left bottom/5rem 1rem no-repeat,
        linear-gradient(#25d4dc, #25d4dc) right bottom/5rem 1rem no-repeat,
        rgba(37, 212, 220, 0.3);
    }
  }
}
</style>
