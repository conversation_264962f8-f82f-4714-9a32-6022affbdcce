<script setup lang="ts">
import emitter from '@/utils/mitt'
import { ref } from 'vue'

const translate = ref(0)

const loadingRef = ref()
const progressRef = ref()

function getTranslateNum(index: number) {
  // 位移
  let t = translate.value - 30 * index
  if (t < 0)
    t = 0
  if (t > 200)
    t = 200
  // 透明度
  const o = (200 - t) / 200
  return [t, o]
}

// 加载完成动画
function hideLoading() {
  const spans = document.querySelectorAll('.loading-text span')
  let num = [0, 0]
  const animate = () => {
    translate.value += 15
    spans.forEach((element: any, index) => {
      num = getTranslateNum(index)
      element.style.transform = `translate(0px, ${num[0]}%)`
      element.style.opacity = num[1]
      if (index === 3) {
        progressRef.value.style.opacity = num[1]
        loadingRef.value.style.opacity = num[1]
      }
    })
    if (num[0] < 200) {
      requestAnimationFrame(animate)
    }
    else {
      loadingRef.value.remove()
    }
  }
  animate()
}

const progress = ref(0)
emitter.on('progress', (e) => {
  progress.value = e
  if (e === 100)
    setTimeout(() => hideLoading(), 1500)
})
</script>

<template>
  <div ref="loadingRef" class="loading">
    <div class="loading-text">
      <span style="--index: 0">L</span>
      <span style="--index: 1">O</span>
      <span style="--index: 2">A</span>
      <span style="--index: 3">D</span>
      <span style="--index: 4">I</span>
      <span style="--index: 5">N</span>
      <span style="--index: 6">G</span>
    </div>
    <div ref="progressRef" class="loading-progress">
      <span class="value">{{ progress }}</span>
      <span class="unit">%</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: #000;
  pointer-events: none;
  z-index: 999;
  &-text {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #fff;
    font-family: D-DIN, Arial, sans-serif;
    letter-spacing: 10rem;
    span {
      font-size: 2vw;
      animation: blurAni 1.5s calc(var(--index) / 5 * 1s) alternate infinite;
    }
  }
  &-progress {
    font-size: 2vw;
    color: #fff;
    font-family: D-DIN, Arial, sans-serif;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    transform-origin: center;
    margin-top: -3vw;
    .unit {
      padding-left: 10rem;
      font-size: 1vw;
    }
  }
  @keyframes blurAni {
    to {
      filter: blur(3rem);
    }
  }
}
</style>
