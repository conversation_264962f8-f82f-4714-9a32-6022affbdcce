/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/login': RouteRecordInfo<'/login', '/login', Record<never, never>, Record<never, never>>,
    '/modules/camera/left': RouteRecordInfo<'/modules/camera/left', '/modules/camera/left', Record<never, never>, Record<never, never>>,
    '/modules/camera/right': RouteRecordInfo<'/modules/camera/right', '/modules/camera/right', Record<never, never>, Record<never, never>>,
    '/modules/common/header': RouteRecordInfo<'/modules/common/header', '/modules/common/header', Record<never, never>, Record<never, never>>,
    '/modules/common/loading': RouteRecordInfo<'/modules/common/loading', '/modules/common/loading', Record<never, never>, Record<never, never>>,
    '/modules/factory/left': RouteRecordInfo<'/modules/factory/left', '/modules/factory/left', Record<never, never>, Record<never, never>>,
    '/modules/factory/right': RouteRecordInfo<'/modules/factory/right', '/modules/factory/right', Record<never, never>, Record<never, never>>,
    '/modules/farm/left': RouteRecordInfo<'/modules/farm/left', '/modules/farm/left', Record<never, never>, Record<never, never>>,
    '/modules/farm/right': RouteRecordInfo<'/modules/farm/right', '/modules/farm/right', Record<never, never>, Record<never, never>>,
    '/modules/home/<USER>': RouteRecordInfo<'/modules/home/<USER>', '/modules/home/<USER>', Record<never, never>, Record<never, never>>,
    '/modules/home/<USER>': RouteRecordInfo<'/modules/home/<USER>', '/modules/home/<USER>', Record<never, never>, Record<never, never>>,
    '/modules/home/<USER>': RouteRecordInfo<'/modules/home/<USER>', '/modules/home/<USER>', Record<never, never>, Record<never, never>>,
    '/modules/home/<USER>': RouteRecordInfo<'/modules/home/<USER>', '/modules/home/<USER>', Record<never, never>, Record<never, never>>,
    '/modules/model/': RouteRecordInfo<'/modules/model/', '/modules/model', Record<never, never>, Record<never, never>>,
  }
}
