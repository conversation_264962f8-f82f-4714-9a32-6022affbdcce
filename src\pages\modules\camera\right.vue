<script setup lang="ts">
import { cameraTokenApi } from '@/service'
import emitter from '@/utils/mitt'
import dayjs from 'dayjs'
import EZUI<PERSON>it from 'ezuikit-js'
import Cookies from 'js-cookie'

const url = ref()
const token = ref(Cookies.get('ezToken') || '')

const cameraRef = ref<HTMLElement>()
const camera = ref<any>()
const template = ref('pcLive')

// 监控尺寸自适应因子
const fontSize = ref(Number.parseFloat(document.documentElement.style.fontSize))
window.addEventListener('resize', () => {
  fontSize.value = Number.parseFloat(document.documentElement.style.fontSize)
  initCamera()
})

async function initCamera() {
  try {
    // 停止已存在的播放
    if (camera.value) {
      stopCamera()
    }
    // 获取token
    if (!token.value) {
      const res = await cameraToken<PERSON>pi()
      if (res.code === 200) {
        token.value = res.data
        Cookies.set('ezToken', token.value, { expires: dayjs().valueOf() + 1000 * 60 * 60 * 24 })
      }
    }
    if (token.value) {
      camera.value = new EZUIKit.EZUIKitPlayer({
        id: 'camera',
        accessToken: token.value,
        url: template.value === 'pcLive' ? url.value.replace('.rec', '.live') : url.value.replace('.live', '.rec'),
        template: template.value,
        audio: 1,
        autoplay: true,
        width: cameraRef.value?.clientWidth || 0,
        height: cameraRef.value?.clientHeight || 0,
      })
    }
  }
  catch (error) {
    console.log(error)
  }
}

async function stopCamera() {
  if (camera.value) {
    await camera.value.stop()
    // await camera.value.destory()
  }
}

onMounted(() => {
  emitter.on('currentCamera', (e) => {
    url.value = `ezopen://open.ys7.com/${e.replace('_', '/')}.live`
    nextTick(() => {
      initCamera()
    })
  })
})

onUnmounted(() => {
  stopCamera()
})

onDeactivated(() => {
  stopCamera()
})

function handleRec() {
  initCamera()
}
</script>

<template>
  <div ref="cameraRef" class="box">
    <div id="camera" />
    <div v-if="url" class="switch flex items-center">
      <span class="m-r-10rem text-light-500">回放</span>
      <NSwitch
        v-model:value="template"
        checked-value="pcRec"
        unchecked-value="pcLive"
        @update:value="handleRec"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: calc(100% - 350rem - 60rem);
  height: 936rem;
  right: 20rem;
  bottom: 20rem;
  padding: 0;
  .switch {
    position: absolute;
    right: 14rem;
    top: 14rem;
    :deep(.n-switch) {
      --n-rail-color: rgba(0, 0, 0, 0.8) !important;
    }
  }
}
:deep(#camera-headControl) {
  background: linear-gradient(270deg, rgba(37, 220, 167, 0) 0%, rgba(37, 220, 167, 0.3) 100%) !important;
}
:deep(#camera-audioControls) {
  background: transparent !important;
  .active {
    svg {
      fill: #25dca7 !important;
    }
  }
}
:deep(#camera-headControl-right) {
  display: none !important;
}
</style>
