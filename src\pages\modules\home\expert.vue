<script setup lang="ts">
import { arrayChunk } from '@/utils'
import emitter from '@/utils/mitt'

const props = defineProps<{
  data: any
}>()

const visible = defineModel('visible', {
  type: Boolean,
  default: false,
})

const farmList = computed(() => {
  return arrayChunk(props.data.filter((item: any) => item.class.includes('1')), 2)
})

const factoryList = computed(() => {
  return arrayChunk(props.data.filter((item: any) => item.class.includes('2')), 2)
})

const current = ref(0)

onMounted(() => {
  emitter.on('homeExpertTab', (val: number) => {
    current.value = val
  })
})
</script>

<template>
  <div class="box">
    <div class="close i-carbon-close-outline cursor-pointer text-18rem text-#2a9374 opacity-70 hover:opacity-50" @click="visible = false" />
    <!-- tab -->
    <NTabs v-model:value="current" animated>
      <NTabPane :name="0" tab="全部">
        <NCarousel
          class="h-830rem w-100%"
          direction="vertical"
          :slides-per-view="4"
          :show-dots="false"
          autoplay
          draggable
        >
          <div v-for="(item, index) in arrayChunk(data, 2)" :key="`all${index}`" class="line flex justify-between">
            <div v-for="(i, j) in item" :key="`all-item${j}`" class="item flex justify-between text-light">
              <div class="avatar m-r-16rem">
                <img class="h-155rem w-125rem b-rd-3rem" :src="i.profilePhoto" alt="">
              </div>
              <div class="info p-t-8rem">
                <div class="name p-l-10rem text-18rem lh-18rem">
                  {{ i.name }}
                </div>
                <div class="m-t-16rem flex items-center justify-between text-14rem">
                  <div class="gender">
                    <span class="opacity-70">性别：</span>{{ i.gender ? '女' : '男' }}
                  </div>
                  <div class="isCPC">
                    <span class="opacity-70">党员：</span>{{ i.isCPC ? '是' : '否' }}
                  </div>
                  <div class="num">
                    <span class="opacity-70">服务次数：</span>{{ i.serviceNumber }}
                  </div>
                </div>
                <div class="desc m-t-5rem text-14rem">
                  <NEllipsis :line-clamp="4" :tooltip="{ contentClass: 'text-#ccc max-w-400rem lh-28rem' }">
                    <span class="opacity-70">个人简介：</span>{{ i.introduction }}
                    <template #tooltip>
                      <div class="p-y-5rem text-18rem text-white">
                        {{ i.name }}的个人简介：
                      </div>
                      <div class="desc-detail">
                        {{ i.introduction }}
                      </div>
                    </template>
                  </NEllipsis>
                </div>
              </div>
            </div>
          </div>
        </NCarousel>
      </NTabPane>
      <NTabPane :name="1" tab="农业">
        <NCarousel
          class="h-830rem w-100%"
          direction="vertical"
          :slides-per-view="4"
          :show-dots="false"
          autoplay
          draggable
        >
          <div v-for="(item, index) in farmList" :key="`farm${index}`" class="line flex justify-between">
            <div v-for="(i, j) in item" :key="`farm-item${j}`" class="item flex items-center justify-between text-light">
              <div class="avatar m-r-16rem">
                <img class="h-155rem w-125rem b-rd-3rem" :src="i.profilePhoto" alt="">
              </div>
              <div class="info p-t-8rem">
                <div class="name p-l-10rem text-18rem lh-18rem">
                  {{ i.name }}
                </div>
                <div class="m-t-16rem flex items-center justify-between text-14rem">
                  <div class="gender">
                    <span class="opacity-70">性别：</span>{{ i.gender ? '女' : '男' }}
                  </div>
                  <div class="isCPC">
                    <span class="opacity-70">党员：</span>{{ i.isCPC ? '是' : '否' }}
                  </div>
                  <div class="num">
                    <span class="opacity-70">服务次数：</span>{{ i.serviceNumber }}
                  </div>
                </div>
                <div class="desc m-t-5rem text-14rem">
                  <NEllipsis :line-clamp="4" :tooltip="{ contentClass: 'text-#ccc max-w-400rem lh-28rem' }">
                    <span class="opacity-70">个人简介：</span>{{ i.introduction }}
                    <template #tooltip>
                      <div class="p-y-5rem text-18rem text-white">
                        {{ i.name }}的个人简介：
                      </div>
                      <div class="desc-detail">
                        {{ i.introduction }}
                      </div>
                    </template>
                  </NEllipsis>
                </div>
              </div>
            </div>
          </div>
        </NCarousel>
      </NTabPane>
      <NTabPane :name="2" tab="工业">
        <NCarousel
          class="h-830rem w-100%"
          direction="vertical"
          :slides-per-view="4"
          :show-dots="false"
          autoplay
          draggable
        >
          <div v-for="(item, index) in factoryList" :key="`factory${index}`" class="line flex justify-between">
            <div v-for="(i, j) in item" :key="`factory-item${j}`" class="item flex items-center justify-between text-light">
              <div class="avatar m-r-16rem">
                <img class="h-155rem w-125rem b-rd-3rem" :src="i.profilePhoto" alt="">
              </div>
              <div class="info p-t-8rem">
                <div class="name p-l-10rem text-18rem lh-18rem">
                  {{ i.name }}
                </div>
                <div class="m-t-16rem flex items-center justify-between text-14rem">
                  <div class="gender">
                    <span class="opacity-70">性别：</span>{{ i.gender ? '女' : '男' }}
                  </div>
                  <div class="isCPC">
                    <span class="opacity-70">党员：</span>{{ i.isCPC ? '是' : '否' }}
                  </div>
                  <div class="num">
                    <span class="opacity-70">服务次数：</span>{{ i.serviceNumber }}
                  </div>
                </div>
                <div class="desc m-t-5rem text-14rem">
                  <NEllipsis :line-clamp="4" :tooltip="{ contentClass: 'text-#ccc max-w-400rem lh-28rem' }">
                    <span class="opacity-70">个人简介：</span>{{ i.introduction }}
                    <template #tooltip>
                      <div class="p-y-5rem text-18rem text-white">
                        {{ i.name }}的个人简介：
                      </div>
                      <div class="desc-detail text-16rem">
                        {{ i.introduction }}
                      </div>
                    </template>
                  </NEllipsis>
                </div>
              </div>
            </div>
          </div>
        </NCarousel>
      </NTabPane>
    </NTabs>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: calc(100% - 464rem - 464rem - 80rem);
  height: 936rem;
  left: 504rem;
  bottom: 20rem;
  padding-top: 0;
  .close {
    position: absolute;
    z-index: 99;
    right: 10rem;
    top: 10rem;
  }
}

.blank {
  border-left: 1rem solid rgba(255, 255, 255, 0.3);
  height: 200rem;
}

:deep(.n-tabs-nav) {
  background: url(@/assets/images/<EMAIL>) no-repeat center top;
  background-size: contain;
  height: 90rem;
  padding-top: 15rem;
  .n-tabs-nav-scroll-content {
    justify-content: center;
  }
  .n-tabs-tab {
    display: flex;
    justify-content: center;
    width: 165rem;
    height: 44rem;
    font-size: 24rem;
    color: #fff;
    &:hover,
    &.n-tabs-tab--active {
      color: #fff;
      text-shadow: 0 0 7rem rgba(255, 209, 138, 0.5);
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: contain;
    }
  }
  .n-tabs-tab-pad,
  .n-tabs-bar {
    display: none;
  }
}
.line {
  .item {
    width: calc(50% - 8rem);
    border: 1rem solid rgba(37, 220, 167, 0.36);
    background: rgba(37, 220, 167, 0.12);
    padding: 16rem;
    .info {
      width: calc(100% - 100rem);
    }
    .name {
      border-left: 4rem solid #25dca7;
    }
  }
}
.desc-detail {
  max-height: 310rem;
  overflow-y: scroll;
  text-indent: 2em;
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
