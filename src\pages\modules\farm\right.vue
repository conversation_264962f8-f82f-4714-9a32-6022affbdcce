<script setup lang="ts">
import Air from './components/air.vue'
import Indoor from './components/indoor.vue'
import Pear from './components/pear.vue'
import Pest from './components/pest.vue'
import Soil from './components/soil.vue'

defineProps<{
  data: any
}>()

const currentAir = ref(0)
onMounted(() => {
  setInterval(() => {
    currentAir.value = currentAir.value === 0 ? 1 : 0
  }, 8000)
})
</script>

<template>
  <div class="box">
    <!-- card1 -->
    <div class="card">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">室外监测</span>
      </div>
      <div class="bd air m-t-20rem">
        <NTabs v-model:value="currentAir" animated>
          <NTabPane :name="0" tab="空气质量">
            <Air :data="data.lastData" />
          </NTabPane>
          <NTabPane :name="1" tab="土壤质量">
            <Soil :data="data.lastData" />
          </NTabPane>
          <NTabPane :name="2" tab="品质检测机">
            <Pear :data="data.lastData" />
          </NTabPane>
        </NTabs>
      </div>
    </div>
    <!-- card2 -->
    <div class="card m-t-20rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">室内监测</span>
      </div>
      <div class="bd indoor m-t-20rem">
        <NTabs animated>
          <NTabPane :name="0" tab="体验馆">
            <Indoor :id="8" />
          </NTabPane>
          <NTabPane :name="1" tab="展示馆">
            <Indoor :id="12" />
          </NTabPane>
          <NTabPane :name="2" tab="文化馆">
            <Indoor :id="2" />
          </NTabPane>
          <NTabPane :name="3" tab="科技馆">
            <Indoor :id="4" />
          </NTabPane>
          <NTabPane :name="4" tab="游客中心">
            <Indoor :id="1" />
          </NTabPane>
        </NTabs>
      </div>
    </div>
    <!-- card3 -->
    <div class="card m-t-20rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">病虫害监测</span>
      </div>
      <div class="bd pest m-t-20rem">
        <Pest />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: 464rem;
  height: 936rem;
  right: 20rem;
  bottom: 20rem;
}
.pest {
  .item {
    background: linear-gradient(180deg, rgba(90, 246, 253, 0) 0%, rgba(167, 212, 230, 0.2) 100%);
    .name {
      background: rgba(90, 246, 253, 0.35);
      border: 2rem solid #5af6fd;
      border-width: 2rem 0;
    }
    .chart {
      position: relative;
      width: 100%;
      height: 120rem;
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        bottom: 0;
      }
    }
    &:nth-child(1) {
      background: linear-gradient(180deg, rgba(255, 192, 96, 0) 0%, rgba(255, 192, 96, 0.2) 100%);
      .name {
        background: rgba(255, 192, 96, 0.35);
        border: 2rem solid #ffc060;
        border-width: 2rem 0;
      }
    }
  }
}
.air {
  :deep(.n-tabs) {
    .n-tabs-tab-pad,
    .n-tabs-bar {
      display: none;
    }
    .n-tabs-nav-scroll-content {
      justify-content: center;
    }
    .n-tabs-wrapper {
      border: 1rem solid rgba(128, 189, 239, 0.26);
      border-radius: 4rem;
      .n-tabs-tab {
        font-size: 14rem;
        padding: 8rem 15rem 6rem;
        line-height: 1;
        color: rgba(255, 255, 255, 0.8);
        border: 1rem solid rgba(37, 220, 167, 0);
      }
      .n-tabs-tab--active {
        color: rgba(37, 220, 167, 1);
        background: rgba(37, 220, 167, 0.16);
        box-shadow: inset 0 0 5rem 0 #25dca7;
        border: 1rem solid rgba(37, 220, 167, 0.8);
      }
      .n-tabs-tab-wrapper:nth-child(2) .n-tabs-tab--active {
        border-radius: 4rem 0 0 4rem;
      }
      .n-tabs-tab-wrapper:nth-child(4) .n-tabs-tab--active {
        border-radius: 0 4rem 4rem 0;
      }
    }
  }
}
.indoor {
  :deep(.n-tabs) {
    .n-tabs-tab-pad,
    .n-tabs-bar {
      display: none;
    }
    .n-tabs-nav-scroll-content {
      justify-content: center;
    }
    .n-tabs-wrapper {
      border: 1rem solid rgba(128, 189, 239, 0.26);
      border-radius: 4rem;
      .n-tabs-tab {
        font-size: 14rem;
        padding: 8rem 15rem 6rem;
        line-height: 1;
        color: rgba(255, 255, 255, 0.8);
        border: 1rem solid rgba(37, 220, 167, 0);
      }
      .n-tabs-tab--active {
        color: rgba(37, 220, 167, 1);
        background: rgba(37, 220, 167, 0.16);
        box-shadow: inset 0 0 5rem 0 #25dca7;
        border: 1rem solid rgba(37, 220, 167, 0.8);
      }
      .n-tabs-tab-wrapper:nth-child(2) .n-tabs-tab--active {
        border-radius: 4rem 0 0 4rem;
      }
      .n-tabs-tab-wrapper:nth-child(7) .n-tabs-tab--active {
        border-radius: 0 4rem 4rem 0;
      }
    }
  }
}
</style>
