<script lang="ts" setup>
import { useEcharts } from '@/hooks/echarts'
// import { graphic } from 'echarts';

import { pestListApi } from '@/service'
import dayjs from 'dayjs'

const chartData = ref()

const { domRef, autoPlay, updateOptions } = useEcharts(() => ({
  grid: {
    left: 10,
    right: 10,
    top: 40,
    bottom: 45,
  },
  tooltip: {
    backgroundColor: 'rgba(0,0,0,.6)',
    padding: 8,
    textStyle: {
      color: 'rgba(255,255,255,.9)',
      fontSize: 12,
    },
    formatter: (params: any) => {
      let html = ''
      html += `<div class="flex items-center justify-between"><span>${params.marker}${params.name}：</span><span class="font-bold font-ddin m-l-5rem">${params.value}只</span></div>`
      return html
    },
  },
  xAxis: {
    data: [],
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      interval: 0,
      color: '#ffffff',
      fontSize: 12,
      margin: 30,
    },
  },
  yAxis: {
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  series: [
    {
      type: 'pictorialBar',
      symbolSize: [
        40,
        12,
      ],
      symbolOffset: [
        0,
        -5,
      ],
      z: 15,
      data: [
        {
          value: 0,
          symbolPosition: 'end',
          itemStyle: {
            color: '#ffcc00',
          },
        },
        {
          value: 0,
          symbolPosition: 'end',
          itemStyle: {
            color: '#00fff5',
          },
        },
        {
          value: 0,
          symbolPosition: 'end',
          itemStyle: {
            color: '#00fff5',
          },
        },
        {
          value: 0,
          symbolPosition: 'end',
          itemStyle: {
            color: '#00fff5',
          },
        },
      ],
    },
    {
      type: 'pictorialBar',
      symbolSize: [
        40,
        12,
      ],
      symbolOffset: [
        0,
        5,
      ],
      z: 12,
      data: [
        {
          value: 0,
          itemStyle: {
            color: '#ff7800',
          },
        },
        {
          value: 0,
          itemStyle: {
            color: '#43bafe',
          },
        },
        {
          value: 0,
          itemStyle: {
            color: '#43bafe',
          },
        },
        {
          value: 0,
          itemStyle: {
            color: '#43bafe',
          },
        },
      ],
    },
    {
      type: 'pictorialBar',
      symbolSize: [
        54,
        18,
      ],
      symbolOffset: [
        0,
        14,
      ],
      z: 11,
      data: [
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#ff7800',
            borderWidth: 5,
          },
        },
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#43bafe',
            borderWidth: 5,
          },
        },
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#43bafe',
            borderWidth: 5,
          },
        },
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#43bafe',
            borderWidth: 5,
          },
        },
      ],
    },
    {
      type: 'pictorialBar',
      symbolSize: [
        72,
        24,
      ],
      symbolOffset: [
        0,
        24,
      ],
      z: 10,
      data: [
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#ff7800',
            borderType: 'dashed',
            borderWidth: 5,
          },
        },
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#43bafe',
            borderType: 'dashed',
            borderWidth: 5,
          },
        },
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#43bafe',
            borderType: 'dashed',
            borderWidth: 5,
          },
        },
        {
          value: 0,
          itemStyle: {
            color: 'transparent',
            borderColor: '#43bafe',
            borderType: 'dashed',
            borderWidth: 5,
          },
        },
      ],
    },
    {
      type: 'bar',
      barWidth: 40,
      data: [
        {
          value: 0,
          label: {
            show: true,
            position: 'top',
            distance: 10,
            color: '#ffcc00',
            fontSize: 18,
          },
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              type: 'linear',
              global: false,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(255,204,0,0.5)',
                },
                {
                  offset: 1,
                  color: '#ff7800',
                },
              ],
            },
          },
        },
        {
          value: 0,
          label: {
            show: true,
            position: 'top',
            distance: 10,
            color: '#00fff5',
            fontSize: 18,
          },
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              type: 'linear',
              global: false,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0,255,245,0.5)',
                },
                {
                  offset: 1,
                  color: '#43bafe',
                },
              ],
            },
          },
        },
        {
          value: 0,
          label: {
            show: true,
            position: 'top',
            distance: 10,
            color: '#00fff5',
            fontSize: 18,
          },
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              type: 'linear',
              global: false,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0,255,245,0.5)',
                },
                {
                  offset: 1,
                  color: '#43bafe',
                },
              ],
            },
          },
        },
        {
          value: 0,
          label: {
            show: true,
            position: 'top',
            distance: 10,
            color: '#00fff5',
            fontSize: 18,
          },
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              type: 'linear',
              global: false,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0,255,245,0.5)',
                },
                {
                  offset: 1,
                  color: '#43bafe',
                },
              ],
            },
          },
        },
      ],
    },
  ],
}))

async function getData() {
  const res = await pestListApi(dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD'))
  if (res.code === 200) {
    if (res.data.length > 0) {
      chartData.value = res.data
    }
    else {
      chartData.value = [
        {
          name: '天牛',
          num: 0,
        },
        {
          name: '叶甲虫',
          num: 0,
        },
        {
          name: '刺蛾',
          num: 0,
        },
        {
          name: '枯叶蛾',
          num: 0,
        },
      ]
    }
  }
}

onMounted(async () => {
  await getData()
  setInterval(async () => await getData(), 1000 * 3600)
})

watch(
  () => chartData.value,
  (nval) => {
    updateOptions((opts) => {
      opts.xAxis.data = nval.map((item: { name: string }) => {
        return item.name
      })
      opts.series.map((series: any) => {
        series.data = series.data.map((item: any, index: number) => {
          item.value = nval[index]?.num || 0
          return item
        })
        return series
      })
      autoPlay(8, 4000)
      return opts
    })
  },
)
</script>

<template>
  <div ref="domRef" class="h-150rem" />
</template>

<style lang="scss" scoped>
</style>
