<script setup>
import emitter from '@/utils/mitt'
import gsap from 'gsap'
import { deepClone } from 'mini3d'
import { World } from './map.js'

// 世界容器
const canvasRef = ref(null)

// 世界对象
const world = ref(null)

// 初始按钮
const initBtnList = [
  { name: '园区情况', checked: false, action: 'InfoPointGroup' },
  { name: '标准桑田', checked: false, action: 'scatterGroup' },
  { name: '产业帮扶', checked: false, action: 'flyLineGroup' },
  { name: '三产数据', checked: false, action: 'homeCenterVisible' },
  // { name: '粒子特效', checked: false, action: 'particleGroup' },
]

// 按钮实例
const btnList = ref(deepClone(initBtnList))

onMounted(() => {
  emitter.on('loadMap', load)
  emitter.on('showHomeCenter', val => btnList.value[3].checked = val)
})

onBeforeUnmount(() => {
  world.value.config.hasUpdate = false
  world.value && world.value.destroy()
})

// 加载地图
function load(assets) {
  world.value = new World(canvasRef.value, assets)
  world.value.config.hasUpdate = false
}

// 入场动画
async function play() {
  world.value.config.hasUpdate = true
  world.value.animateTl.timeScale(1) // 设置播放速度正常
  world.value.animateTl.play()
  showBtn()
}

// 显示按钮
function showBtn() {
  gsap.to('.map .control .btn', { y: 0, stagger: 0.1 })
}

// 隐藏按钮
async function hideBtn() {
  // 还原状态
  btnList.value = deepClone(initBtnList)
  btnList.value.map(changeGroupVisible)
  gsap.to('.map .control .btn', { y: '200%', stagger: 0.1 })
}

// 切换按钮状态
function changeBtnChecked(item) {
  if (item.name === '三产数据') {
    emitter.emit('homeCenterVisible', true)
    return
  }
  item.checked = !item.checked
  changeGroupVisible(item)
}

// 切换组显隐
function changeGroupVisible(item) {
  switch (item.name) {
    case '园区情况':
      world.value[item.action].visible = item.checked
      world.value.barGroup.visible = !item.checked
      world.value.allProvinceLabel.map(label => item.checked ? label.hide() : label.show())
      if (!item.checked) {
        clearInterval(world.value.infoPointLabelTime)
        world.value.infoLabelElement.map((label) => {
          label.visible = false
        })
      }
      else {
        world.value.createInfoPointLabelLoop()
      }
      break
    case '产业帮扶':
      world.value.flyLineFocusGroup.visible = item.checked
      break
    case '三产数据':
      emitter.emit('showHomeCenter', item.checked)
      return
    case '粒子特效':
      world.value.particles.enable = item.checked
      break
  }
  world.value[item.action].visible = item.checked
}

// 暴露给父组件调用
defineExpose({ world, play })
</script>

<template>
  <div class="world map">
    <canvas ref="canvasRef" />
    <div class="control">
      <div v-for="(item, index) in btnList" :key="index" class="btn" :class="{ active: item.checked }" @click="changeBtnChecked(item)">
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.world.map {
  .info-point {
    background: rgba(22, 74, 59, 0.45);
    color: #2cd7a6;
    font-size: 14rem;
    width: 170rem;
    height: 126rem;
    padding: 16rem 12rem 0;
    margin-bottom: 30rem;
    will-change: transform;
    &-wrap {
      &:after,
      &:before {
        display: block;
        content: '';
        position: absolute;
        top: 0;
        width: 10rem;
        height: 10rem;
        border-top: 1rem solid #2a9374;
      }
      &:before {
        left: 0;
        border-left: 1rem solid #2a9374;
      }
      &:after {
        right: 0;
        border-right: 1rem solid #2a9374;
      }
      &-inner {
        &:after,
        &:before {
          display: block;
          content: '';
          position: absolute;
          bottom: 0;
          width: 10rem;
          height: 10rem;
          border-bottom: 1rem solid #2a9374;
        }
        &:before {
          left: 0;
          border-left: 1rem solid #2a9374;
        }
        &:after {
          right: 0;
          border-right: 1rem solid #2a9374;
        }
      }
    }
    &-line {
      position: absolute;
      top: 7rem;
      right: 12rem;
      display: flex;
      .line {
        width: 5rem;
        height: 2rem;
        margin-right: 5rem;
        background: #17e5c3;
      }
    }
    &-content {
      .content-item {
        display: flex;
        height: 28rem;
        line-height: 28rem;
        background: rgba(0, 0, 0, 0.35);
        margin-bottom: 5rem;
        .label {
          width: 60rem;
          padding-left: 10rem;
        }
        .value {
          color: #ffffff;
        }
      }
    }
  }
  .provinces-label {
    &-wrap {
      transform: translate(50%, 200%);
      opacity: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 18rem;

      height: 53rem;
      border-radius: 30rem 30rem 30rem 0rem;
      background: rgba(0, 0, 0, 0.4);
      margin-bottom: -40rem;
    }
    .number {
      color: #fff;
      font-size: 30rem;
      font-weight: 700;

      .unit {
        color: #fff;
        font-size: 12rem;
        font-weight: 400;
        opacity: 0.5;
        padding-left: 5rem;
        padding-right: 10rem;
      }
    }
    .name {
      color: #fff;
      font-size: 16rem;
      font-weight: 700;
      span {
        display: block;
      }
      .en {
        color: #fff;
        font-size: 10rem;
        opacity: 0.5;
        font-weight: 700;
      }
    }
    .no {
      padding-left: 10rem;
      color: #7efbf6;
      text-shadow:
        0 0 5rem #7efbf6,
        0 0 10rem #7efbf6;
      font-size: 30rem;
      font-weight: 700;
    }
    .yellow {
      .no {
        color: #fef99e !important;
        text-shadow:
          0 0 5rem #fef99e,
          0 0 10rem #fef99e !important;
      }
    }
  }
  .china-label {
    color: #fff;

    font-size: 12rem;
    will-change: transform;
    .other-label {
      display: flex;
      align-items: center;
      padding: 5rem;
      border-radius: 4rem;
      background: rgba(0, 0, 0, 0.6);
      will-change: transform;
    }

    &.blur {
      filter: blur(2rem);
      opacity: 0.5;
    }
    .label-icon {
      display: block;
      width: 20rem;
      height: 20rem;
      margin: 0 10rem 0 0;
    }
  }
  .map-label {
    padding: 5rem;
    color: #fff;
    will-change: transform;
    font-size: 36rem;
    font-weight: bold;
    text-align: center;
    letter-spacing: 4.5rem;
    -webkit-box-reflect: below 0 -webkit-linear-gradient(transparent, transparent 20%, rgba(255, 255, 255, 0.3));
    .other-label {
      display: flex;
      flex-direction: column;
    }
    span {
      font-size: 46rem;
      &:last-child {
        font-size: 16rem;
        font-weight: normal;
        letter-spacing: 0rem;
        color: #a8f0dc;
      }
    }
  }
  .area-label {
    .other-label {
      color: #fff;
      opacity: 1;
      transform: translateY(100%);
    }
  }
  .decoration-label {
    padding-bottom: 10rem;
    &.reflect {
      -webkit-box-reflect: below 0 -webkit-linear-gradient(transparent, transparent 20%, rgba(255, 255, 255, 0.3));
    }
    .label-icon {
      display: block;
      width: 40rem;
      height: 40rem;
    }
  }
  .other-label {
    transform: translateY(200%);
    opacity: 0;
    background: none;
    will-change: transform;
  }
}
</style>
