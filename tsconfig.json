{"compilerOptions": {"target": "es2016", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["./src/*"], "~/*": ["./*"]}, "resolveJsonModule": true, "types": ["vite/client", "vite-plugin-pwa/client", "unplugin-vue-macros/macros-global", "unplugin-vue-router/client", "naive-ui/volar", "@types/three"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["**/*.tsx", "**/*.ts", "**/*.vue", "**/*.d.ts"], "exclude": ["dist", "node_modules", "eslint.config.js"]}