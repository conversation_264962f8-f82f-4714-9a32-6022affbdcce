<script lang="ts" setup>
defineProps<{
  data: any
}>()
</script>

<template>
  <div class="air">
    <div class="flex flex-wrap items-center justify-between tracking-tight">
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            通道号
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_P }}</span>
            <span class="m-l-2rem text-14rem opacity-90" />
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            缺陷等级
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_N }}</span>
            <span class="m-l-2rem text-14rem opacity-90" />
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            数量
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_Temperature }}</span>
            <span class="m-l-2rem text-14rem opacity-90">个</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="m-l-2rem text-14rem opacity-90">
            果茎
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Air_Humidity }}</span>
            <span class="m-l-2rem text-14rem opacity-90">cm</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            重量
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_PH }}</span>
            <span class="m-l-2rem text-14rem opacity-90">克</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            糖度值
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_K }}</span>
            <span class="m-l-2rem text-14rem opacity-90">Brix</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.air {
  .item {
    .info {
      width: calc(100% - 36rem);
      height: 45rem;
      padding: 0 24rem;
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: contain;
    }
  }
}
</style>
