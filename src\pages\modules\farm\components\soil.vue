<script lang="ts" setup>
defineProps<{
  data: any
}>()
</script>

<template>
  <div class="air">
    <div class="flex flex-wrap items-center justify-between tracking-tight">
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            温度
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_Temperature }}</span>
            <span class="m-l-2rem text-14rem opacity-90">℃</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="m-l-2rem text-14rem opacity-90">
            湿度
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Air_Humidity }}</span>
            <span class="m-l-2rem text-14rem opacity-90">%</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            PH
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_PH }}</span>
            <span class="m-l-2rem text-14rem opacity-90">ph</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            氮
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_N }}</span>
            <span class="m-l-2rem text-14rem opacity-90">mg/kg</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            磷
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_P }}</span>
            <span class="m-l-2rem text-14rem opacity-90">mg/kg</span>
          </div>
        </div>
      </div>
      <div class="item m-y-5rem w-49% flex items-center justify-between">
        <img class="h-36rem w-36rem" src="@/assets/images/<EMAIL>" alt="">
        <div class="info flex items-center justify-between text-white">
          <div class="text-14rem opacity-90">
            钾
          </div>
          <div>
            <span class="text-18rem font-bold font-ddin">{{ data.Soil_K }}</span>
            <span class="m-l-2rem text-14rem opacity-90">mg/kg</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.air {
  .item {
    .info {
      width: calc(100% - 36rem);
      height: 45rem;
      padding: 0 24rem;
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: contain;
    }
  }
}
</style>
