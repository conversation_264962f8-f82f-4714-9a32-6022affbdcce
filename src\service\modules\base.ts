import { request } from '../helper'

// 获取字典
export function dictApi() {
  return request<any>({
    url: '/Dict/cockpit',
    method: 'get',
    params: { _page: 1, _limit: 1000, _sort: 'id', _order: 'asc' },
  })
}

// 获取专家
export function expertApi() {
  return request<any>({
    url: '/Expert',
    method: 'get',
    params: { _page: 1, _limit: 1000, _sort: 'putTopTime,id', _order: 'desc,asc' },
  })
}

// 获取桑树品种
export function treeApi() {
  return request<any>({
    url: '/Dict/dictionary',
    method: 'get',
    params: { _page: 1, _limit: 1000, _sort: 'id', _order: 'asc' },
  })
}

// 获取商品
export function goodApi() {
  return request<any>({
    url: '/Good',
    method: 'get',
    params: { _page: 1, _limit: 1000, _sort: 'id', _order: 'asc' },
  })
}

// 获取产品列表
// export function productApi() {
//   return request<Api.Base.Tree>({
//     url: '/Dict/dictionary',
//     method: 'get',
//     params: { _page: 1, _limit: 1000, _sort: 'id', _order: 'asc' }
//   })
// }

// 获取建筑物列表
export function buildingListApi(areaId: number) {
  return request<string[]>({
    url: '/Building/building',
    method: 'get',
    params: { _page: 1, _limit: 1000, _sort: 'id', _order: 'asc', areaId },
  })
}

// 获取设备列表
export function deviceListApi() {
  return request<any>({
    url: '/Device',
    method: 'get',
    params: { _page: 1, _limit: 1000, _sort: 'id', _order: 'asc' },
  })
}

// 获取监控列表
export function cameraListApi() {
  return request<any>({
    url: '/Monitor',
    method: 'get',
  })
}

// 获取萤石云token
export function cameraTokenApi() {
  return request<string>({
    url: '/Monitor/token',
    method: 'get',
  })
}

// 获取虫情
export function pestListApi(begin: string, end: string) {
  return request<any>({
    url: '/Cockpit/GetAnalyseDataByDate',
    method: 'get',
    params: { begin, end },
  })
}

// 获取警报日志
export function warnListApi() {
  return request<any>({
    url: '/Warn/record',
    method: 'get',
    params: { _page: 1, _limit: 10, _sort: 'id', _order: 'desc' },
  })
}

// 获取空气传感器数据
export function sensorAirApi(deviceId: number) {
  return request<any>({
    url: '/Chart/sensor',
    method: 'get',
    params: { deviceId },
  })
}

// 获取温振传感器数据
export function sensorShakeApi(areaId: number, day: string) {
  return request<any>({
    url: '/Chart/frequent',
    method: 'get',
    params: { areaId, day },
  })
}
