import { request } from '../helper'

// 登录接口
export function login<PERSON><PERSON>(username: string, password: string) {
  return request<string>({
    url: '/login/bypwd',
    method: 'post',
    headers: {
      'X-App-Code': 'manager',
    },
    data: {
      loginName: username,
      password,
    },
  })
}

// yny
export function ynyLoginApi(query: { timestamp: string, sign: string }) {
  return request<string>({
    url: '/YanNongYun',
    method: 'get',
    headers: {
      'X-App-Code': 'manager',
    },
    params: query,
  })
}
