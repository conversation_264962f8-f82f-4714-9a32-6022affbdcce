declare namespace Api {
  /** Auth */
  namespace Auth {
    // 登录
    interface LoginParams {
      username: string
      password: string
    }

    interface LoginResponse {
      token: string
    }
  }

  /** Base */
  namespace Base {
    interface Dict {
      code: string
      id: number
      name: string
      type: number
      value: string
    }
    interface Tree {
      type: number
      title: string
      coverId: number
      coverUr: string
      content: string
    }
    interface Grid {
      id: number
      gridName: string
      gridArea: number
      gridManager: string
      phoneNumber: string
      treeTypes: TreeType[]
    }
    interface ParkImage {
      fileId: number
      fileName: string
      fuleUrl: string
    }
    interface Place {
      description: string
      id: number
      imageId: number
      imageUrl: string
      name: string
    }
    interface Product {
      description: string
      id: number
      imageId: number
      imageUrl: string
      name: string
    }
    // 获取园区基础信息
    interface FarmInfo {
      grids: Grid[]
      parkImages: ParkImage[]
      parkIntroduction: string
      places: Place[]
      products: Product[]
      treeTypes: TreeType[]
    }

    // 获取气象站数据
    interface GrowSensor {
      air_Atmos: number
      air_CO2: number
      air_Humidity: number
      air_JiaQuan: number
      air_PM10: number
      air_PM25: number
      air_SO2: number
      air_TVOC: number
      air_Temperature: number
      deviceId: number
      id: number
      imei: string
      receivedTime: string
      soil_EC: number
      soil_Humidity: number
      soil_K: number
      soil_N: number
      soil_P: number
      soil_PH: number
      soil_Temperature: number
    }

    // 获取虫情数据
    interface Pest {
      name: string
      num: number
    }
    type PestList = Pest[]

    // 获取所有设备
    interface Device {
      deviceName: string
      deviceSpecific: string
      deviceType: number
      id: number
      imei: string
      lastConnectTime: string
      lastData: string
      metaData: { ezopen: string }
      onlineStatus: boolean
    }
    type DeviceList = Device[]
  }
}
