import antfu from '@antfu/eslint-config'

export default antfu(
  {
    unocss: true,
    formatters: true,
  },
  {
    ignores: ['public/draco/', 'packages/'],
  },
  {
    rules: {
      'vue/multi-word-component-names': 'off',
      'vue/component-name-in-template-casing': [
        'warn',
        'PascalCase',
        {
          registeredComponentsOnly: false,
          ignores: ['/^icon-/'],
        },
      ],
      'max-params': ['error', 10],
      'unocss/order-attributify': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'vue/no-static-inline-styles': 'off',
      'no-console': 'off',
      'no-underscore-dangle': 'off',
      'ts/ban-ts-comment': 'off',
      'no-new': 'off',
      'no-unused-vars': 'off',
      'array-callback-return': 'off',
      'unused-imports/no-unused-vars': 'off',
      'regexp/no-super-linear-backtracking': 'off',
      'regexp/no-useless-quantifier': 'off',
    },
  },
)
