// 自适应
export function autofit() {
  document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`)
  const fontSize
    = document.documentElement.clientWidth / document.documentElement.clientHeight < 16 / 9
      ? document.documentElement.clientWidth / 1920
      : document.documentElement.clientHeight / 1080
  document.documentElement.style.fontSize = `${fontSize}px`
}

export function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export function arrayChunk<T>(array: Array<T>, size = 1): Array<any> {
  // 边界检测
  const length = array.length
  if (!length || size < 1)
    return []

  let index = 0
  let resIndex = 0 // 改为let，因为需要递增
  const result = Array.from({ length: Math.ceil(length / size) })

  while (index < length) {
    result[resIndex] = array.slice(index, index + size) // 修复切片逻辑
    index += size
    resIndex++ // 递增结果数组索引
  }

  return result
}
