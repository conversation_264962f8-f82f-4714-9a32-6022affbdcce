<script setup lang="ts">
import { cameraListApi } from '@/service'
import emitter from '@/utils/mitt'
import dayjs from 'dayjs'

const cameraList = ref()
const current = ref()

onMounted(() => {
  cameraListApi().then((res) => {
    if (res.code === 200) {
      cameraList.value = res.data.map((item: any) => {
        item.imageUrl = import.meta.env.VITE_STATIC_BASE_URL + item.imageUrl
        return item
      })
      // play(current.value)
    }
  })
})

function play(index: number) {
  current.value = index
  emitter.emit('currentCamera', cameraList.value[index].deviceImei)
}

function isOnline(date: string) {
  const now = dayjs()
  return now.diff(dayjs(date), 'hour') < 12
}
</script>

<template>
  <div class="box">
    <!-- card1 -->
    <div class="card">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">监控设备列表</span>
      </div>
      <div class="bd m-t-20rem">
        <NCarousel
          class="h-830rem w-100%"
          direction="vertical"
          :slides-per-view="4"
          :show-dots="false"
          :loop="false"
          draggable
          show-arrow
        >
          <div v-for="(item, index) in cameraList" :key="index" class="item" :class="{ active: current === index }" @click="play(index)">
            <div class="cover h-195rem">
              <div class="title" :class="isOnline(item.lastConnectTime) ? 'online' : 'offline'">
                {{ item.areaName }} - {{ item.deviceName }}
              </div>
              <img class="h-100% w-100%" :src="item.imageUrl" alt="">
            </div>
          </div>
        </NCarousel>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: 350rem;
  height: 936rem;
  left: 20rem;
  bottom: 20rem;
}
.item {
  cursor: pointer;
  .cover {
    position: relative;
    padding: 8rem;
    border-radius: 4rem;
    border: 1rem solid rgba(37, 220, 167, 0.2);
  }
  .title {
    position: absolute;
    left: 8rem;
    top: 8rem;
    width: calc(100% - 16rem);
    height: 36rem;
    line-height: 36rem;
    background: rgba(0, 47, 32, 0.5);
    padding: 0 20rem;
    font-size: 14rem;
    &:before {
      content: '';
      position: absolute;
      left: 10rem;
      top: 15rem;
      width: 4rem;
      height: 4rem;
      border-radius: 50%;
    }
    &.online {
      &:before {
        background: #25dca7;
      }
    }
    &.offline {
      &:before {
        background: #ff5e5e;
      }
    }
  }
  &:hover,
  &.active {
    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 257rem;
      height: 10rem;
      background: url('@/assets/images/<EMAIL>') no-repeat;
      background-size: contain;
    }
    &::before {
      top: -4rem;
      left: 20rem;
    }
    &::after {
      bottom: 8rem;
      left: 10rem;
    }
    .cover {
      border: 1rem solid rgba(37, 220, 167, 0.5);
    }
  }
}
</style>
