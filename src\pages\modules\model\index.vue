<script setup>
import emitter from '@/utils/mitt'
import gsap from 'gsap'
import { Assets } from './assets.js'
import Factory from './components/factory.vue'
import Farm from './components/farm.vue'
import Map from './components/map.vue'
import Sensor from './components/sensor.vue'

const props = defineProps({
  data: Object,
})

const current = ref(0)

const mapRef = ref()
const farmRef = ref()
const factoryRef = ref()

const assets = ref(null)
const progress = ref(0)

// 传感器信息框
const sensor = ref(null)

onMounted(() => {
  emitter.on('current', changeCurrent)
  initAssets(async () => {
    // 加载全部场景
    emitter.emit('loadMap', assets.value)
    emitter.emit('loadFarm', assets.value)
    emitter.emit('loadFactory', assets.value)
    await hideLoading()
    // 默认显示地图
    mapRef.value.play()
  })
  emitter.on('sensor', (data) => {
    sensor.value = data
  })
  // 模型数据
  emitter.emit('modelData', props.data)
})

onBeforeUnmount(() => {
  assets.value = null
})

function initAssets(onLoadCallback) {
  assets.value = new Assets()
  // 资源加载进度
  const params = { progress: 0 }
  assets.value.instance.on('onProgress', (path, itemsLoaded, itemsTotal) => {
    gsap.to(params, {
      progress: Math.floor(itemsLoaded / itemsTotal) * 100,
      onUpdate: () => {
        progress.value = Math.floor(params.progress)
      },
    })
  })
  // 资源加载完成
  assets.value.instance.on('onLoad', () => {
    onLoadCallback && onLoadCallback()
  })
}

// 隐藏loading
async function hideLoading() {
  return new Promise((resolve, reject) => {
    const tl = gsap.timeline()
    tl.to('.loading-text span', { y: '200%', opacity: 0, ease: 'power4.inOut', duration: 2, stagger: 0.2 })
    tl.to('.loading-progress', { opacity: 0, ease: 'power4.inOut', duration: 2 }, '<')
    tl.to('.loading', { opacity: 0, ease: 'power4.inOut', onComplete: () => resolve() }, '-=1')
  })
}

function changeCurrent(val) {
  current.value = val
  sensor.value = null
  nextTick(() => {
    switch (current.value) {
      case 0:
        mapRef.value.play()
        break
      case 1:
        farmRef.value.play()
        emitter.emit('leftRightVisible', farmRef.value.group === 0)
        break
      case 2:
        factoryRef.value.play()
        emitter.emit('leftRightVisible', factoryRef.value.group === 0)
        break
    }
  })
}

watch(
  () => sensor.value,
  (nval, oval) => {
    if (farmRef.value.group === 2 && nval === null && oval !== null && oval.position !== undefined)
      farmRef.value.world.flyTo(2)
  },
)
</script>

<template>
  <div class="model">
    <!-- loading动画 -->
    <div class="loading">
      <div class="loading-text">
        <span v-for="(item, index) in 'LOADING'" :key="index" :style="`--index: ${index + 1}`">{{ item }}</span>
      </div>
      <div class="loading-progress">
        <span v-if="progress === 0" class="value text-18rem">加载模型中...</span>
        <template v-else>
          <span class="value">{{ progress }}</span>
          <span class="unit">%</span>
        </template>
      </div>
    </div>
    <!-- 地图 -->
    <Map ref="mapRef" :class="{ show: current === 0 || current === 3 }" />
    <!-- 桑田 -->
    <Farm ref="farmRef" :class="{ show: current === 1 }" />
    <!-- 工厂 -->
    <Factory ref="factoryRef" :class="{ show: current === 2 }" />
    <!-- 传感器信息框 -->
    <Transition name="right">
      <Sensor v-if="sensor" style="--index: 1; transition-delay: 0s;" :data="sensor" />
    </Transition>
  </div>
</template>

<style lang="scss">
@import '@/styles/model.scss';

.model {
  .loading {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: #000;
    pointer-events: none;
    z-index: 9999;
    &-text {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      color: #fff;
      font-family: 'D-DIN', Arial, sans-serif;
      letter-spacing: 10px;
      span {
        font-size: 2vw;
        animation: blurAni 1.5s calc(var(--index) / 5 * 1s) alternate infinite;
      }
    }
    &-progress {
      font-size: 2vw;
      color: #fff;
      font-family: 'D-DIN', Arial, sans-serif;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      transform-origin: center;
      margin-top: -3vw;
      .unit {
        padding-left: 10px;
        font-size: 1vw;
      }
    }
  }
  @keyframes blurAni {
    to {
      filter: blur(3px);
    }
  }
}
</style>
