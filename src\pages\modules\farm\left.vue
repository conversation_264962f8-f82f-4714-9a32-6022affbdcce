<script setup lang="ts">
import dayjs from 'dayjs'

const props = defineProps<{
  data: any
  device: any
}>()

const baseImageUrl = import.meta.env.VITE_STATIC_BASE_URL

const online = computed(() => {
  return props.device.filter((item: any) => dayjs().diff(dayjs(item.lastConnectTime), 'hour') < 24).length
})

const current = ref(0)
const currentList = ref(props.device)
function changeCurrent(index: number) {
  current.value = index
  switch (index) {
    case 0:
      currentList.value = props.device
      break
    case 1:
      currentList.value = props.device.filter((item: any) => dayjs().diff(dayjs(item.lastConnectTime), 'hour') < 24)
      break
    case 2:
      currentList.value = props.device.filter((item: any) => dayjs().diff(dayjs(item.lastConnectTime), 'hour') >= 24)
      break
  }
}
</script>

<template>
  <div class="box">
    <!-- card1 -->
    <div class="card">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">园区介绍</span>
      </div>
      <div class="bd m-t-20rem">
        <div class="swiper w-full">
          <NCarousel class="h-240rem w-100%" autoplay draggable>
            <div v-for="(item, index) in data.album" :key="`album${index}`" class="item">
              <img class="h-240rem w-100%" :src="baseImageUrl + item">
            </div>
          </NCarousel>
        </div>
        <div class="desc m-t-20rem h-124rem text-14rem">
          {{ data.intro }}
        </div>
      </div>
    </div>
    <!-- card2 -->
    <div class="card m-t-20rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">设备状态</span>
      </div>
      <div class="bd m-t-20rem">
        <!-- count -->
        <div class="flex items-center justify-between">
          <div class="w-35% flex cursor-pointer items-center" @click="changeCurrent(0)">
            <img class="h-56rem w-56rem" src="@/assets/images/<EMAIL>" alt="">
            <div class="p-l-10rem">
              <div class="text-14rem">
                设备数量
              </div>
              <div class="text-18rem color-#5AF6FD" :class="{ 'font-bold font-ddin': current === 0 }">
                {{ device.length }}
              </div>
            </div>
          </div>
          <div class="online w-65% flex items-center justify-between p-y-5rem text-center">
            <div class="w-49% cursor-pointer" @click="changeCurrent(1)">
              <div class="flex items-center justify-center">
                <img class="h-14rem w-14rem" src="@/assets/images/<EMAIL>" alt="">
                <span class="m-l-5rem text-14rem">正常</span>
              </div>
              <div class="text-18rem color-#25DCA7" :class="{ 'font-bold font-ddin': current === 1 }">
                {{ online }}
              </div>
            </div>
            <div class="blank" />
            <div class="w-49% cursor-pointer" @click="changeCurrent(2)">
              <div class="flex items-center justify-center">
                <img class="h-14rem w-14rem" src="@/assets/images/<EMAIL>" alt="">
                <span class="m-l-5rem text-14rem">异常</span>
              </div>
              <div class="text-18rem color-#FFAB2B" :class="{ 'font-bold font-ddin': current === 2 }">
                {{ device.length - online }}
              </div>
            </div>
          </div>
        </div>
        <!-- list -->
        <div class="list m-t-10rem">
          <div class="flex items-center justify-between p-y-5rem text-16rem font-bold">
            <div class="w-30%">
              设备名称
            </div>
            <div class="w-46% p-l-16rem">
              设备编码
            </div>
            <div class="w-24% p-r-16rem text-right">
              设备状态
            </div>
          </div>
          <NCarousel
            class="h-230rem w-100%"
            direction="vertical"
            :slides-per-view="6"
            :show-dots="false"
            :interval="1000"
            autoplay
            draggable
          >
            <div v-for="(item, index) in currentList" :key="`device${index}`" class="item h-100% flex items-center justify-between text-14rem">
              <div class="w-30% opacity-80">
                {{ item.deviceSpecific }}{{ item.deviceName }}
              </div>
              <div class="w-46% p-l-16rem opacity-80">
                {{ item.deviceImei }}
              </div>
              <div class="w-24% p-r-16rem text-right">
                <i v-if="dayjs().diff(dayjs(item.lastConnectTime), 'hour') < 24" class="text-#25DCA7">在线</i>
                <i v-else class="text-#FFAB2B">离线</i>
              </div>
            </div>
          </NCarousel>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: 464rem;
  height: 936rem;
  left: 20rem;
  bottom: 20rem;
}
.desc {
  text-indent: 2em;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 3rem;
    background: rgba(37, 220, 167, 0.1);
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(37, 220, 167, 0.25);
    &:hover {
      background: rgba(37, 220, 167, 0.75);
      cursor: pointer;
    }
  }
}
.swiper {
  position: relative;
  border: 1rem solid rgba(37, 220, 167, 0.2);
  border-radius: 4rem;
  padding: 5rem;
  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 257rem;
    height: 10rem;
    background: url('@/assets/images/<EMAIL>') no-repeat;
    background-size: contain;
    transition: all 0.5s ease;
  }
  &::before {
    top: -6rem;
    left: 150rem;
  }
  &::after {
    bottom: -6rem;
    left: -10rem;
  }
  &:hover {
    &::before {
      left: -10rem;
    }
    &::after {
      left: 150rem;
    }
  }
  .item {
    position: relative;
    .title {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 36rem;
      line-height: 36rem;
      background: rgb(0, 48, 34, 0.5);
      padding-left: 15rem;
      font-size: 16rem;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 8rem;
        width: 4rem;
        height: 20rem;
        background: #25dca7;
        border-radius: 0 4rem 4rem 0;
      }
    }
  }
  // :deep(.n-carousel__dots) {
  //   bottom: 45rem;
  // }
}
.online {
  background: linear-gradient(180deg, rgba(90, 246, 253, 0.3) 0%, rgba(90, 246, 253, 0) 100%);
  .blank {
    border-left: 1rem solid rgba(255, 255, 255, 0.2);
    height: 28rem;
  }
}
.list {
  .item {
    line-height: 1;
  }
  .n-carousel__slide:nth-child(2n) {
    .item {
      background: rgba(90, 246, 253, 0.08);
    }
  }
}
</style>
