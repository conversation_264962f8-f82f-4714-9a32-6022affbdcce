import dayjs from 'dayjs'

export const sensorLabel = {
  Imei: { label: '设备码', unit: '' },
  Timestamp: { label: '采集时间', unit: '', formatter: (e: string) => dayjs(e).format('YYYY-MM-DD HH:mm:ss') },

  // 空气 温振
  Acc: { label: '加速度', unit: 'm/s2' },
  CO2: { label: '二氧化碳', unit: 'ppm' },
  H2S: { label: '硫化氢', unit: 'ppm' },
  PM25: { label: 'PM2.5', unit: 'μg/m3' },
  Light: { label: '光照', unit: 'Lux' },
  Speed: { label: '速度', unit: 'mm/s' },
  Offset: { label: '位移', unit: 'μm' },
  Humidity: { label: '湿度', unit: '%' },
  Frequency: { label: '频率', unit: 'Hz' },
  Temperature: { label: '温度', unit: '℃' },

  // 虫情
  Battery: { label: '电池电量', unit: '%' },
  Voltage: { label: '电池电压', unit: 'V' },
  FanStatus: { label: '风机状态 ', unit: '', formatter: (e: boolean) => e ? '开启' : '关闭' },
  LampStatus: { label: '引虫灯状态', unit: '', formatter: (e: boolean) => e ? '开启' : '关闭' },
  RainStatus: { label: '降雨状态', unit: '', formatter: (e: boolean) => e ? '有雨' : '无雨' },
  LightStatus: { label: '光照状态', unit: '', formatter: (e: boolean) => e ? '白天' : '晚上' },
  AnalyseTime: { label: '分析时间', unit: '', formatter: (e: string) => dayjs(e).format('YYYY-MM-DD HH:mm:ss') },

  // 气象站
  Soil_K: { label: '钾', unit: 'mg/kg' },
  Soil_N: { label: '氮', unit: 'mg/kg' },
  Soil_P: { label: '磷', unit: 'mg/kg' },
  Air_CO2: { label: '二氧化碳', unit: 'ppm' },
  Air_SO2: { label: '二氧化硫', unit: 'ppm' },
  Soil_EC: { label: '土壤电导率', unit: 'μS/cm' },
  Soil_PH: { label: '土壤PH值', unit: 'ph' },
  Air_PM10: { label: 'PM10', unit: 'ppm' },
  Air_PM25: { label: 'PM2.5', unit: 'ppm' },
  Air_TVOC: { label: 'TVOC', unit: 'ppm' },
  Air_Atmos: { label: '大气压', unit: 'kPa' },
  Air_JiaQuan: { label: '甲醛', unit: 'ppm' },
  Air_Humidity: { label: '空气湿度', unit: '%' },
  Soil_Humidity: { label: '土壤湿度', unit: '%' },
  Air_Temperature: { label: '空气温度', unit: '℃' },
  Soil_Temperature: { label: '土壤温度', unit: '℃' },
}
