<script setup lang="ts">
import { createTextVNode, defineComponent } from 'vue'

defineOptions({
  name: 'AppProvider',
})

const ContextHolder = defineComponent({
  name: 'ContextHolder',
  setup() {
    window.$message = useMessage()
    window.$notification = useNotification()
    return () => createTextVNode()
  },
})
</script>

<template>
  <NNotificationProvider>
    <NMessageProvider>
      <ContextHolder />
      <slot />
    </NMessageProvider>
  </NNotificationProvider>
</template>

<style scoped></style>
