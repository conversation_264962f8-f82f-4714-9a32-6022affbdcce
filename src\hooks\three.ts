import * as THREE from 'three'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js'

import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { Water } from 'three/examples/jsm/objects/Water.js'

export function useThree(context: any) {
  // 创建场景
  const scene = new THREE.Scene()

  // 添加环境光
  const ambientLight = new THREE.AmbientLight(0xFFFFFF, 0.3)
  scene.add(ambientLight)

  // 添加平行光
  const directionalLight = new THREE.DirectionalLight(0xFFFFFF, 1)
  directionalLight.position.set(1000, -100, 900)
  scene.add(directionalLight)

  // 创建相机
  const camera = new THREE.PerspectiveCamera(60, context.drawingBufferWidth / context.drawingBufferHeight, 100, 1 << 30)

  // 初始化加载器
  const textureLoader = new THREE.TextureLoader()
  const dracoLoader = new DRACOLoader()
  dracoLoader.setDecoderPath('draco/')
  const gltfLoader = new GLTFLoader()
  gltfLoader.setDRACOLoader(dracoLoader)

  // 初始化渲染器
  const renderer = new THREE.WebGLRenderer({
    context,
    antialias: true,
    alpha: true,
    powerPreference: 'high-performance',
  })
  // 自动清空画布这里必须设置为 false，否则地图底图将无法显示
  renderer.autoClear = false
  // @ts-ignore
  renderer.outputEncoding = THREE.sRGBEncoding

  // 获取传入模型模型的世界坐标
  const getModelWorldPosition = (model: any) => {
    scene.updateMatrixWorld(true)
    const worldPosition = new THREE.Vector3()
    model.getWorldPosition(worldPosition)
    return worldPosition
  }

  // 添加辅助
  const initHelper = () => {
    // 辅助坐标系
    scene.add(new THREE.AxesHelper(100))
    // 创建一个Raycaster对象
    const raycaster = new THREE.Raycaster()
    const mouse = new THREE.Vector2()
    // 添加鼠标点击事件监听
    window.addEventListener(
      'click',
      (event: any) => {
        // 计算鼠标在屏幕上的位置（归一化设备坐标）
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1
        mouse.y = -(event.clientY / window.innerHeight) * 2 + 1
        // 更新射线的起点和方向
        raycaster.setFromCamera(mouse, camera)
        // 计算射线与场景中物体的交点
        const intersects = raycaster.intersectObjects(scene.children, true)
        if (intersects.length > 0) {
          console.log(intersects[0].object, getModelWorldPosition(intersects[0].object))
        }
      },
      false,
    )
  }

  // 重置相机
  const resetCamera = (params: {
    near: number
    far: number
    fov: number
    position: [number, number, number]
    up: [number, number, number]
    lookAt: [number, number, number]
  }) => {
    // 这里必须执行！！重新设置 three 的 gl 上下文状态。
    renderer.resetState()

    const { near, far, fov, position, up, lookAt } = params
    camera.near = near// 近平面
    camera.far = far // 远平面
    camera.fov = fov // 视野范围
    camera.position.set(...position)
    camera.up.set(...up)
    camera.lookAt(...lookAt)
    camera.updateProjectionMatrix()

    // 这里必须执行！重新设置 three 的 gl 上下文状态
    renderer.render(scene, camera)
    renderer.resetState()
  }

  // 创建水纹
  const createWater = (object: any) => {
    const water = new Water(object.geometry, {
      textureWidth: 512,
      textureHeight: 512,
      waterNormals: textureLoader.load('./model/waternormals.jpg', (texture: any) => {
        texture.wrapS = THREE.RepeatWrapping
        texture.wrapT = THREE.RepeatWrapping
      }),
      alpha: 1.0,
      sunDirection: new THREE.Vector3(),
      sunColor: 0xFFFFFF,
      waterColor: 0x1C7ED6,
      distortionScale: 3.7,
      fog: scene.fog !== undefined,
    })
    // 模型命名
    water.name = `${object.name}_water`
    console.log(object)
    // 波浪模型相对河道位置
    water.position.set(object.position.x, object.position.y + 0.1, object.position.z)
    // 波浪角度同河道角度
    water.rotation.set(object.rotation.x, object.rotation.y, object.rotation.z)
    // 添加到父模型
    object.parent.add(water)
    // return water
  }

  // 创建金属材质
  const createMaterial = (object: any) => {
    object.material = new THREE.MeshStandardMaterial({
      color: 0xFFFFFF,
      metalness: 1.0, // 金属度，0.0为非金属，1.0为金属
      roughness: 0.5, // 粗糙度
      metalnessMap: null,
      emissive: 0xEEEEEE,
      emissiveIntensity: 0.3,
    })
    textureLoader.load('./model/material.jpg', (texture: any) => {
      texture.wrapS = THREE.RepeatWrapping
      texture.wrapT = THREE.RepeatWrapping
      object.material.metalnessMap = texture
      object.material.needsUpdate = true
    })
  }

  return {
    scene,
    camera,
    renderer,
    textureLoader,
    gltfLoader,
    initHelper,
    resetCamera,
    createWater,
    createMaterial,
  }
}
