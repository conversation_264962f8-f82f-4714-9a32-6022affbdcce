import labelIcon6 from '@/assets/images/gongye_ico_jian<PERSON><EMAIL>'
import labelIcon4 from '@/assets/images/<EMAIL>'
import labelIcon5 from '@/assets/images/<EMAIL>'
import labelIcon1 from '@/assets/images/<EMAIL>'
import labelIcon3 from '@/assets/images/<EMAIL>'
import labelIcon2 from '@/assets/images/<EMAIL>'
import emitter from '@/utils/mitt'
import { geoMercator } from 'd3-geo'
import gsap from 'gsap'

import { Label3d, Mini3d } from 'mini3d'
import {
  AmbientLight,
  CubeTextureLoader,
  DirectionalLight,
  Fog,
  Group,
  MeshStandardMaterial,
  RepeatWrapping,
  Vector3,
} from 'three'
import { InteractionManager } from 'three.interactive'
import { toRaw } from 'vue'
import factoryData from '../data/factoryData'

// 设备数据
emitter.on('deviceData', (e) => {
  factoryData.map((item) => {
    switch (item.type) {
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
        item.data = e.filter(i => typeof item.id === 'number' ? item.id === i.id : item.id.includes(i.id))[0]?.lastData
        if (typeof item.id === 'number') {
          item.data = e.filter(i => i.id === item.id)[0]?.lastData
        }
        else {
          item.data = item.id.map(j => e.filter(k => k.id === j)[0]?.lastData)
        }
        break
    }
    return item
  })
})

const labelIconMap = [labelIcon1, labelIcon2, labelIcon3, labelIcon4, labelIcon5, labelIcon6]

// 视角列表
const targetMap = [
  {
    position: { x: 0, y: 10, z: 30 },
    target: { x: 0, y: 0, z: 0 },
  },
  {
    position: { x: -115, y: 12, z: -168 },
    target: { x: -130, y: 0, z: -174 },
  },
  {
    position: { x: 0, y: 18, z: 22 },
    target: { x: 0, y: 0, z: 0 },
  },
  {
    position: { x: 168, y: 15, z: 125 },
    target: { x: 168, y: 0, z: 108 },
  },
]

export class World extends Mini3d {
  constructor(canvas, assets) {
    super(canvas)
    // 中心坐标
    this.geoProjectionCenter = [120.468811, 32.682227]
    // 缩放比例
    this.geoProjectionScale = 500000
    // 雾
    this.scene.fog = new Fog(0x10372C, 100, 500)
    // 创建天空盒
    const cubeTextureLoader = new CubeTextureLoader()
    this.scene.background = cubeTextureLoader.load([
      './model/sky/px.png', // 右
      './model/sky/nx.png', // 左
      './model/sky/py.png', // 上
      './model/sky/ny.png', // 下
      './model/sky/pz.png', // 前
      './model/sky/nz.png', // 后
    ])

    // 相机初始位置
    this.camera.instance.position.set(30, 30, 80)
    this.camera.instance.near = 1
    this.camera.instance.far = 10000
    this.camera.controls.minPolarAngle = 0
    this.camera.controls.maxPolarAngle = Math.PI / 2
    this.camera.instance.updateProjectionMatrix()

    // 相机初始目标
    const { x, y, z } = targetMap[0].target
    this.camera.controls.target.set(x, y, z)
    this.camera.instance.updateProjectionMatrix()

    // 创建交互管理
    this.interactionManager = new InteractionManager(this.renderer.instance, this.camera.instance, this.canvas)
    // 播放状态
    this.playing = false
    this.assets = assets
    // 创建环境光
    this.initEnvironment()
    // 辅助线
    if (import.meta.env.MODE === 'development')
      this.setAxesHelper(100)
    this.init()
  }

  init() {
    // 标签组
    this.labelGroup = new Group()
    this.label3d = new Label3d(this)
    this.labelGroup.rotation.x = -Math.PI / 2
    this.scene.add(this.labelGroup)
    // 创建标签
    this.createLabel()
    // 创建地图
    this.createMap()
    // 创建动画时间线
    const tl = gsap.timeline()
    tl.pause()
    this.animateTl = tl
    tl.to(this.camera.instance.position, {
      duration: 2,
      ...targetMap[0].position,
      ease: 'circ.out',
      onStart: () => {
        // this.labelGroup.visible = false
      },
    })
  }

  initEnvironment() {
    const sun = new AmbientLight(0xFFFFFF, 1)
    this.scene.add(sun)
    const directionalLight = new DirectionalLight(0xFFFFFF, 1)
    directionalLight.position.set(-300, 60, -80)
    directionalLight.castShadow = true
    directionalLight.shadow.radius = 20
    directionalLight.shadow.mapSize.width = 1024
    directionalLight.shadow.mapSize.height = 1024
    this.scene.add(directionalLight)
  }

  createMap() {
    this.factoryA = this.createFactoryA()
    this.factoryA.visible = false
    this.factoryB = this.createFactoryB()
    this.factoryC = this.createFactoryC()
    this.factoryC.visible = false
  }

  // 加载A厂
  createFactoryA() {
    const gltf = toRaw(this.assets.instance.getResource('factoryA'))
    // 模型模型
    gltf.scene.traverse((child) => {
      // 如果模型的材质存在
      if (child.isMesh) {
        // 调整材质反光
        child.material.emissive = child.material.color
          .clone()
          .multiplyScalar(
            child.material.name.includes('sm01_y')
            || child.material.name.includes('DiMian_03')
            || child.material.name.includes('sangshu02')
            || child.material.name.includes('grassHRs')
            || child.material.name.includes('CAIDI')
              ? 0.7
              : 1,
          )
        child.material.emissiveMap = child.material.map
      }
    })

    // 模型位置
    const [x, z] = this.geoProjection([120.4539, 32.699021])
    gltf.scene.position.set(x, 0, z)

    // 计算 geoMercator 的单位距离，用于缩放模型
    const unitDistance = 40075017 / (2 * Math.PI * this.geoProjectionScale)
    const scaleFactor = 1.5 / unitDistance
    gltf.scene.scale.set(scaleFactor, scaleFactor, scaleFactor)

    // 隐藏模型
    gltf.scene.visible = false

    this.scene.add(gltf.scene)

    return gltf.scene
  }

  // 加载B厂
  createFactoryB() {
    const gltf = toRaw(this.assets.instance.getResource('factoryB'))
    // 模型模型
    gltf.scene.traverse((child) => {
      // 如果模型的材质存在
      if (child.isMesh) {
        // 调整材质反光
        child.material.emissive = child.material.color
          .clone()
          .multiplyScalar(
            child.material.name.includes('sm01_y')
            || child.material.name.includes('DiMian')
            || child.material.name.includes('sangshu02')
            || child.material.name.includes('grassHRs')
            || child.material.name.includes('CAIDI')
              ? 0.7
              : 1,
          )
        child.material.emissiveMap = child.material.map
        // 创建金属材质
        if (child.name === 'B_XHT' || child.name === 'B_tong') {
          const texture = this.assets.instance.getResource('material')
          texture.wrapS = RepeatWrapping
          texture.wrapT = RepeatWrapping
          child.material = new MeshStandardMaterial({
            color: 0xEEEEEE,
            metalness: 0.5, // 金属度，0.0为非金属，1.0为金属
            roughness: 0.8, // 粗糙度
            metalnessMap: texture,
            emissive: 0xEEEEEE,
            emissiveIntensity: 0.2,
          })
        }
      }
    })

    // 模型位置
    const [x, z] = this.geoProjection([120.468311, 32.682227])
    gltf.scene.position.set(x, 0, z)

    // 计算 geoMercator 的单位距离，用于缩放模型
    const unitDistance = 40075017 / (2 * Math.PI * this.geoProjectionScale)
    const scaleFactor = 1.5 / unitDistance
    gltf.scene.scale.set(scaleFactor, scaleFactor, scaleFactor)

    this.scene.add(gltf.scene)

    return gltf.scene
  }

  // 加载C厂
  createFactoryC() {
    const gltf = toRaw(this.assets.instance.getResource('factoryC'))
    // 模型模型
    gltf.scene.traverse((child) => {
      // 如果模型的材质存在
      if (child.isMesh) {
        // 调整材质反光
        child.material.emissive = child.material.color
          .clone()
          .multiplyScalar(
            child.material.name.includes('sm01_y')
            || child.material.name.includes('DiMian_03')
            || child.material.name.includes('sangshu02')
            || child.material.name.includes('grassHRs')
            || child.material.name.includes('CAIDI')
              ? 0.7
              : 1,
          )
        child.material.emissiveMap = child.material.map
      }
    })

    // 模型位置
    const [x, z] = this.geoProjection([120.487316, 32.672423])
    gltf.scene.position.set(x, 0, z)

    // 计算 geoMercator 的单位距离，用于缩放模型
    const unitDistance = 40075017 / (2 * Math.PI * this.geoProjectionScale)
    const scaleFactor = 1.5 / unitDistance
    gltf.scene.scale.set(scaleFactor, scaleFactor, scaleFactor)

    // 隐藏模型
    gltf.scene.visible = false

    this.scene.add(gltf.scene)

    return gltf.scene
  }

  createLabel() {
    const self = this
    const labelGroup = this.labelGroup
    const label3d = this.label3d
    const labelList = []
    factoryData.map((item) => {
      const label = labelStyle01(item, label3d, labelGroup)
      labelList.push(label)
    })
    this.labelList = labelList
    function labelStyle01(userData, label3d, labelGroup) {
      const label = label3d.create('', `factory-label`, true)
      const [x, y] = self.geoProjection(userData.center)
      label.init(
        `<div class="sensor-label group-${userData.group} ${userData.icon || ''}">
          <img class="icon" src="${labelIconMap[userData.type - 1]}">
          <div class="name type-${userData.type}">${userData.name}</div>
        </div>`,
        new Vector3(x, -y, 0.4),
      )
      label3d.setLabelStyle(label, 0.02, 'x')
      label.setParent(labelGroup)
      label.userData = userData

      // 添加点击事件
      label.element.addEventListener('click', (event) => {
        event.stopPropagation() // 阻止冒泡以避免触发其他事件
        emitter.emit('sensor', userData)
      })

      return label
    }
  }

  flyTo(group) {
    switch (group) {
      case 1:
        this.factoryA.visible = true
        break
      case 2:
        this.factoryB.visible = true
        break
      case 3:
        this.factoryC.visible = true
        break
      default:
        this.factoryB.visible = true
        break
    }

    // 终止所有动画
    gsap.globalTimeline.clear()

    const target = new Vector3(...this.camera.controls.target)
    gsap.to(target, {
      duration: 0.5,
      ...targetMap[group].target,
      ease: 'circ.out',
      onUpdate: () => {
        this.camera.controls.target.set(...target)
        this.camera.controls.update()
      },
    })

    gsap.to(this.camera.instance.position, {
      duration: 1,
      ...targetMap[group].position,
      ease: 'circ.out',
      onComplete: () => this.showLabel(group),
    })
  }

  showLabel(group = 0) {
    this.labelList.map((label, index) => {
      const element = label.element.querySelector('.sensor-label')
      gsap.to(
        element,
        label.userData.group === group
          ? {
              duration: 1,
              delay: 0.1 * (index % 5),
              translateY: 0,
              opacity: 1,
              ease: 'circ.out',
              onComplete: () => element.classList.add('show'),
            }
          : {
              duration: 1,
              delay: 0.1 * (index % 5),
              translateY: '200%',
              opacity: 0,
              ease: 'circ.out',
              onComplete: () => element.classList.remove('show'),
            },
      )
    })
  }

  geoProjection(args) {
    return geoMercator().center(this.geoProjectionCenter).scale(this.geoProjectionScale).translate([0, 0])(args)
  }

  update() {
    super.update()
    this.interactionManager && this.interactionManager.update()
  }

  destroy() {
    super.destroy()
    this.label3d && this.label3d.destroy()
  }
}
