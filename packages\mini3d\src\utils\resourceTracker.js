import { Material, Texture, Object3D, Mesh } from "three"

export class ResourceTracker {
  resources = new Set()
  constructor() {}
  track(resource) {
    if (!resource) {
      return resource
    }

    if (Array.isArray(resource)) {
      resource.forEach((resource) => this.track(resource))
      return resource
    }

    if (resource.type !== "Scene" && (resource.dispose || resource instanceof Object3D)) {
      this.resources.add(resource)
    }
    if (resource instanceof Object3D) {
      this.track(resource.geometry)
      this.track(resource.material)
      this.track(resource.children)
    } else if (resource instanceof Material) {
      for (const value of Object.values(resource)) {
        if (value instanceof Texture) {
          this.track(value)
        }
      }

      if (resource.uniforms) {
        for (const value of Object.values(resource.uniforms)) {
          if (value) {
            const uniformValue = value.value
            if (uniformValue instanceof Texture || Array.isArray(uniformValue)) {
              this.track(uniformValue)
            }
          }
        }
      }
    }
    return resource
  }
  untrack(resource) {
    this.resources.delete(resource)
  }
  dispose() {
    try {
      for (const resource of this.resources) {
        if (resource instanceof Object3D) {
          if (resource.parent) {
            resource.parent.remove(resource)
          }
        }
        if (resource.dispose) {
          if (resource.type === 1009) {
            resource.dispose()
          }
          resource.dispose()
        }
        !!resource.clear && resource.clear()
      }
      this.resources.clear()
    } catch (error) {
      console.log(error)
    }
  }
}
