<script lang="ts" setup>
import { sensorLabel } from '@/composables/sensor'
import emitter from '@/utils/mitt'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const current = ref(0)

const lastData = computed(() => {
  return typeof props.data.id === 'number' ? props.data.data : props.data.data[current.value]
})

function parseAnalyseData(jsonString: string | undefined | null) {
  if (!jsonString)
    return []
  try {
    return JSON.parse(jsonString)
  }
  catch (e) {
    console.error('JSON解析错误:', e)
    return []
  }
}

const labels: { [key: string]: any } = sensorLabel
</script>

<template>
  <div class="sensor p-b-16rem" :class="`type-${data.type}`">
    <div class="hd flex items-center justify-between">
      <div class="text-18rem text-white">
        {{ data.name }}
      </div>
      <div class="i-carbon-close cursor-pointer text-18rem text-white opacity-60 hover:opacity-100" @click="emitter.emit('sensor', null)" />
    </div>
    <div class="bd">
      <!-- 如果多个设备，则显示切换按钮 -->
      <div v-if="typeof data.id === 'object'" class="m-b-10px flex flex-wrap items-center gap-10px">
        <NTag v-for="(item, index) in data.id" :key="`tag-${index}`" class="cursor-pointer" :class="current === index ? 'active' : ''" :bordered="false" @click="current = index">
          设备{{ item }}
        </NTag>
      </div>
      <template v-if="lastData?.switch">
        <!-- 建筑物 -->
        <template v-if="data.type === 6">
          <div class="item flex items-center text-16rem text-white">
            <img :src="lastData.coverUrl">
          </div>
          <div class="item flex items-center indent-2em text-16rem text-white">
            {{ lastData.description }}
          </div>
        </template>
        <!-- 病虫害基站 -->
        <template v-else-if="data.type === 1">
          <div v-for="list in lastData" :key="`list-${list.id || Math.random()}`" class="m-b-20rem list-item">
            <div v-if="list.AnalyseImageUrl" class="item flex items-center text-16rem text-white">
              <img :src="`https://fuan.kscss.com/external/jnrsmcu/wormImages/${list.AnalyseImageUrl.split('/').pop()}`">
            </div>
            <div class="item flex items-center text-16rem text-white">
              <div class="label opacity-80">
                分析时间 :
              </div>
              <div class="m-l-20rem">
                {{ list.AnalyseTime || '--' }}
              </div>
            </div>
            <div class="item flex items-center text-16rem text-white">
              <div class="label min-w-75rem opacity-80">
                分析结果 :
              </div>
              <div class="m-l-20rem">
                <template v-if="parseAnalyseData(list.AnalyseData).length > 0">
                  <i
                    v-for="(i, k) in parseAnalyseData(list.AnalyseData)"
                    :key="k"
                    class="insect m-b-5rem mr-5rem inline-block b-rd-2rem px-5rem text-white line-height-22rem"
                  >
                    {{ i.name }}：{{ i.num }}
                  </i>
                </template>
                <i v-else>暂未发现病虫害</i>
              </div>
            </div>
          </div>
        </template>
        <!-- 气象站 -->
        <!-- 空气质量传感器 -->
        <!-- 温振传感器 -->
        <!-- 太阳能杀虫灯 -->
        <template v-else>
          <template v-for="item in Object.keys(lastData)" :key="item">
            <div v-if="labels[item] && lastData[item] !== null" class="item flex items-center text-16rem text-white">
              <div class="label opacity-80">
                {{ (sensorLabel as any)[item].label }} :
              </div>
              <div class="m-l-20rem">
                {{ labels[item].hasOwnProperty('formatter') ? labels[item].formatter(lastData[item]) : lastData[item] }} {{ labels[item].unit }}
              </div>
            </div>
          </template>
        </template>
      </template>
      <div v-else>
        <div class="flex items-center justify-center p-y-40px text-16rem text-gray-500">
          设备待机中，可在小程序端开启
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sensor {
  position: absolute;
  z-index: 99;
  width: 368rem;
  right: 20rem;
  top: 200rem;
  background: rgba(0, 0, 0, 0.65);
  box-shadow: 0 0 8rem rgba(0, 0, 0, 0.2);
  &::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: -6rem;
    width: 24rem;
    height: 2rem;
  }
  .hd {
    position: relative;
    height: 40rem;
    padding: 0 16rem;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: -6rem;
      width: 24rem;
      height: 2rem;
    }
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: -6rem;
      width: 24rem;
      height: 2rem;
    }
  }
  .bd {
    position: relative;
    padding: 16rem 16rem 0;
    max-height: 700rem;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      display: none;
    }
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 1rem;
    }
    .item {
      border-bottom: 1rem solid rgba(255, 255, 255, 0.1);
      margin-bottom: 10rem;
      &:last-child {
        margin-bottom: 0;
      }
      img {
        width: 100%;
        line-height: 1;
      }
    }
  }
  &.type-1 {
    border: 1rem solid #25d4dc80;
    &::after {
      background: #25d4dc;
    }
    .hd {
      background: linear-gradient(270deg, rgba(37, 212, 220, 0) 0%, rgba(37, 212, 220, 0.38) 100%);
      &::before,
      &::after {
        background: #25d4dc;
      }
    }
    .bd {
      &::after {
        background: linear-gradient(90deg, rgba(37, 212, 220, 0.62), rgba(37, 212, 220, 0));
      }
      .n-tag {
        background: rgba(37, 212, 220, 0.2);
        color: rgba(37, 212, 220, 0.4);
        &.active {
          color: #25d4dc;
        }
      }
      .item .label {
        border-bottom: 1rem solid #25d4dc;
      }
      .insect {
        background: rgba(12, 166, 120, 0.3);
        border: 1rem solid rgba(12, 166, 120, 0.8);
      }
    }
  }
  &.type-2 {
    border: 1rem solid #ffab2b80;
    &::after {
      background: #ffab2b;
    }
    .hd {
      background: linear-gradient(270deg, rgba(255, 171, 43, 0) 0%, rgba(255, 171, 43, 0.38) 100%);
      &::before,
      &::after {
        background: #ffab2b;
      }
    }
    .bd {
      &::after {
        background: linear-gradient(90deg, rgba(255, 171, 43, 0.62), rgba(255, 171, 43, 0));
      }
      .n-tag {
        background: rgba(255, 171, 43, 0.2);
        color: rgba(255, 171, 43, 0.4);
        &.active {
          color: #ffab2b;
        }
      }
      .item .label {
        border-bottom: 1rem solid #ffab2b;
      }
    }
  }
  &.type-3 {
    border: 1rem solid #25dca780;
    &::after {
      background: #25dca7;
    }
    .hd {
      background: linear-gradient(270deg, rgba(37, 220, 167, 0) 0%, rgba(37, 220, 167, 0.38) 100%);
      &::before,
      &::after {
        background: #25dca7;
      }
    }
    .bd {
      &::after {
        background: linear-gradient(90deg, rgba(37, 220, 167, 0.62), rgba(37, 220, 167, 0));
      }
      .n-tag {
        background: rgba(37, 220, 167, 0.2);
        color: rgba(37, 220, 167, 0.4);
        &.active {
          color: #25dca7;
        }
      }
      .item .label {
        border-bottom: 1rem solid #25dca7;
      }
    }
  }
  &.type-4 {
    border: 1rem solid #23d46780;
    &::after {
      background: #23d467;
    }
    .hd {
      background: linear-gradient(270deg, rgba(35, 212, 103, 0) 0%, rgba(35, 212, 103, 0.38) 100%);
      &::before,
      &::after {
        background: #23d467;
      }
    }
    .bd {
      &::after {
        background: linear-gradient(90deg, rgba(35, 212, 103, 0.62), rgba(35, 212, 103, 0));
      }
      .n-tag {
        background: rgba(35, 212, 103, 0.2);
        color: rgba(35, 212, 103, 0.4);
        &.active {
          color: #23d467;
        }
      }
      .item .label {
        border-bottom: 1rem solid #23d467;
      }
    }
  }
  &.type-5 {
    border: 1rem solid #ff752b80;
    &::after {
      background: #ff752b;
    }
    .hd {
      background: linear-gradient(270deg, rgba(255, 117, 43, 0) 0%, rgba(255, 117, 43, 0.38) 100%);
      &::before,
      &::after {
        background: #ff752b;
      }
    }
    .bd {
      &::after {
        background: linear-gradient(90deg, rgba(255, 117, 43, 0.62), rgba(255, 117, 43, 0));
      }
      .n-tag {
        background: rgba(255, 117, 43, 0.2);
        color: rgba(255, 117, 43, 0.4);
        &.active {
          color: #ff752b;
        }
      }
      .item .label {
        border-bottom: 1rem solid #ff752b;
      }
    }
  }
  &.type-6 {
    border: 1rem solid #0a6e9780;
    &::after {
      background: #0a6e97;
    }
    .hd {
      background: linear-gradient(270deg, rgba(10, 110, 151, 0) 0%, rgba(10, 110, 151, 0.38) 100%);
      &::before,
      &::after {
        background: #0a6e97;
      }
    }
    .bd {
      &::after {
        background: linear-gradient(90deg, rgba(10, 110, 151, 0.62), rgba(10, 110, 151, 0));
      }
      .n-tag {
        background: rgba(10, 110, 151, 0.2);
        color: rgba(10, 110, 151, 0.4);
        &.active {
          color: #0a6e97;
        }
      }
      .item .label {
        border-bottom: 1rem solid #0a6e97;
      }
    }
  }
}
</style>
