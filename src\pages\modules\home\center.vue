<script setup lang="ts">
import { arrayChunk } from '@/utils'
import Area from './components/area.vue'
import Shop from './components/shop.vue'

// 接口
import { goodApi, treeApi } from '@/service'

defineProps<{
  data: any
}>()

const baseImageUrl = import.meta.env.VITE_STATIC_BASE_URL

const visible = defineModel('visible', {
  type: Boolean,
  default: false,
})

const treeList = ref<any>([])
const goodList = ref<any>([])

onMounted(() => {
  treeApi().then((res) => {
    if (res.code === 200) {
      treeList.value = res.data.data
    }
  })
  goodApi().then((res) => {
    if (res.code === 200) {
      goodList.value = res.data.data
    }
  })
})
</script>

<template>
  <div class="box">
    <div class="close i-carbon-close-outline cursor-pointer text-18rem text-#2a9374 opacity-70 hover:opacity-50" @click="visible = false" />
    <!-- tab -->
    <NTabs animated>
      <NTabPane :name="0" tab="一产">
        <div class="flex justify-between">
          <!-- card1 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">种植品种</span>
            </div>
            <div class="bd tree m-t-3rem">
              <NCarousel
                class="h-380rem w-100%"
                direction="vertical"
                :slides-per-view="3"
                :show-dots="false"
                autoplay
                draggable
              >
                <div v-for="(item, index) in treeList" :key="`tree${index}`" class="item flex justify-between">
                  <div class="cover">
                    <img class="h-108rem w-108rem" :src="baseImageUrl + item.coverUrl" alt="">
                  </div>
                  <div class="info">
                    <div class="title">
                      {{ item.title }}
                    </div>
                    <div class="desc m-t-5rem text-14rem opacity-90">
                      <NEllipsis :line-clamp="3" :tooltip="false">
                        {{ item.content }}
                      </NEllipsis>
                    </div>
                  </div>
                </div>
              </NCarousel>
            </div>
          </div>
          <div class="blank" />
          <!-- card2 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">种植情况</span>
            </div>
            <div class="bd expert area m-t-20rem">
              <div class="total flex items-center justify-between">
                <div>流转土地面积</div>
                <div>
                  <span class="text-28rem font-bold font-ddin">{{ data.exchange_area }}</span>
                  <span class="m-l-5rem text-14rem opacity-90">亩</span>
                </div>
              </div>
              <div class="chart m-t-30rem">
                <Area :data="data.plantinfo" />
              </div>
            </div>
          </div>
        </div>
        <!-- card3 -->
        <div class="card m-t-20rem">
          <div class="hd flex items-center">
            <img src="@/assets/images/<EMAIL>">
            <span class="m-l-25rem">产业规模</span>
          </div>
          <div class="bd expert scale m-t-20rem">
            <div class="flex items-center justify-between">
              <div class="item flex flex-col items-center justify-center">
                <img class="h-80rem w-80rem" src="@/assets/images/<EMAIL>" alt="">
                <div class="m-t-15rem flex items-center justify-center">
                  <span class="text-16rem opacity-80">种植面积</span>
                  <template v-if="data.scale[0].rate > 0">
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#25DCA7">{{ data.scale[0].rate }}%</span>
                  </template>
                  <template v-else>
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#FFAB2B">{{ data.scale[0].rate }}%</span>
                  </template>
                </div>
                <div class="m-t-10rem">
                  <span class="text-28rem font-bold font-ddin">{{ data.scale[0].value }}</span>
                  <span class="m-l-5rem text-14rem opacity-90">亩</span>
                </div>
              </div>
              <div class="item flex flex-col items-center justify-center">
                <img class="h-80rem w-80rem" src="@/assets/images/<EMAIL>" alt="">
                <div class="m-t-15rem flex items-center justify-center">
                  <span class="text-16rem opacity-80">投资金额</span>
                  <template v-if="data.scale[1].rate">
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#25DCA7">{{ data.scale[1].rate }}%</span>
                  </template>
                  <template v-else>
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#FFAB2B">{{ data.scale[1].rate }}%</span>
                  </template>
                </div>
                <div class="m-t-10rem">
                  <span class="text-28rem font-bold font-ddin">{{ data.scale[1].value }}</span>
                  <span class="m-l-5rem text-14rem opacity-90">万元</span>
                </div>
              </div>
              <div class="item flex flex-col items-center justify-center">
                <img class="h-80rem w-80rem" src="@/assets/images/<EMAIL>" alt="">
                <div class="m-t-15rem flex items-center justify-center">
                  <span class="text-16rem opacity-80">产业园总产值</span>
                  <template v-if="data.scale[2].rate">
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#25DCA7">{{ data.scale[2].rate }}%</span>
                  </template>
                  <template v-else>
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#FFAB2B">{{ data.scale[2].rate }}%</span>
                  </template>
                </div>
                <div class="m-t-10rem">
                  <span class="text-28rem font-bold font-ddin">{{ data.scale[2].value }}</span>
                  <span class="m-l-5rem text-14rem opacity-90">万元</span>
                </div>
              </div>
              <div class="item flex flex-col items-center justify-center">
                <img class="h-80rem w-80rem" src="@/assets/images/<EMAIL>" alt="">
                <div class="m-t-15rem flex items-center justify-center">
                  <span class="text-16rem opacity-80">产业园总值</span>
                  <template v-if="data.scale[3].rate">
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#25DCA7">{{ data.scale[3].rate }}%</span>
                  </template>
                  <template v-else>
                    <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
                    <span class="color-#FFAB2B">{{ data.scale[3].rate }}%</span>
                  </template>
                </div>
                <div class="m-t-10rem">
                  <span class="text-28rem font-bold font-ddin">{{ data.scale[3].value }}</span>
                  <span class="m-l-5rem text-14rem opacity-90">万元</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </NTabPane>
      <NTabPane :name="1" tab="二产">
        <div class="flex justify-between">
          <!-- card1 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">产品种类</span>
            </div>
            <div class="bd product m-t-3rem">
              <NCarousel
                class="h-380rem w-100%"
                direction="vertical"
                :slides-per-view="3"
                :show-dots="false"
                autoplay
                draggable
              >
                <div v-for="(item, index) in goodList" :key="`good${index}`" class="item flex justify-between">
                  <div class="cover">
                    <img class="h-108rem w-108rem" :src="baseImageUrl + item.coverUrl" alt="">
                  </div>
                  <div class="info">
                    <div class="title">
                      {{ item.title }}
                    </div>
                    <div class="desc m-t-5rem text-14rem opacity-90">
                      <NEllipsis :line-clamp="3" :tooltip="false">
                        {{ item.summary }}
                      </NEllipsis>
                    </div>
                  </div>
                </div>
              </NCarousel>
            </div>
          </div>
          <div class="blank m-x-20rem m-t-150rem" />
          <!-- card2 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">销售情况</span>
            </div>
            <div class="bd expert sale m-t-20rem">
              <div class="flex flex-wrap justify-between">
                <div class="item">
                  <div class="title">
                    线上销售平台
                  </div>
                  <div class="p-y-10rem">
                    <span class="text-28rem font-bold font-ddin">{{ data.saleinfo[0] }}</span>
                    <span class="m-l-5rem text-14rem opacity-90">个</span>
                  </div>
                </div>
                <div class="item">
                  <div class="title">
                    国内代销城市
                  </div>
                  <div class="p-y-10rem">
                    <span class="text-28rem font-bold font-ddin">{{ data.saleinfo[1] }}</span>
                    <span class="m-l-5rem text-14rem opacity-90">个</span>
                  </div>
                </div>
                <div class="item">
                  <div class="title">
                    线上销售金额
                  </div>
                  <div class="p-y-10rem">
                    <span class="text-28rem font-bold font-ddin">{{ data.saleinfo[2] }}</span>
                    <span class="m-l-5rem text-14rem opacity-90">万元</span>
                  </div>
                </div>
                <div class="item">
                  <div class="title">
                    线下销售金额
                  </div>
                  <div class="p-y-10rem">
                    <span class="text-28rem font-bold font-ddin">{{ data.saleinfo[3] }}</span>
                    <span class="m-l-5rem text-14rem opacity-90">万元</span>
                  </div>
                </div>
                <div class="item">
                  <div class="title">
                    线上销量
                  </div>
                  <div class="p-y-10rem">
                    <span class="text-28rem font-bold font-ddin">{{ data.saleinfo[4] }}</span>
                    <span class="m-l-5rem text-14rem opacity-90">万件</span>
                  </div>
                </div>
                <div class="item">
                  <div class="title">
                    线下销量
                  </div>
                  <div class="p-y-10rem">
                    <span class="text-28rem font-bold font-ddin">{{ data.saleinfo[5] }}</span>
                    <span class="m-l-5rem text-14rem opacity-90">万件</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-between">
          <!-- card3 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">店铺累计销售额占比</span>
            </div>
            <div class="bd m-t-10rem">
              <Shop :data="data.saleradar" />
            </div>
          </div>
          <div class="blank m-x-20rem m-t-100rem" />
          <!-- card4 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">TOP3</span>
            </div>
            <div class="bd expert rank m-t-20rem">
              <div class="wrap flex items-end justify-center">
                <div class="item m-b-20rem flex flex-col items-center">
                  <img class="h-65rem" src="@/assets/images/<EMAIL>" alt="">
                  <div class="m-t-30rem text-18rem">
                    {{ data.top3[1] }}
                  </div>
                </div>
                <div class="item m-b-80rem flex flex-col items-center">
                  <img class="h-65rem" src="@/assets/images/<EMAIL>" alt="">
                  <div class="m-t-30rem text-18rem">
                    {{ data.top3[0] }}
                  </div>
                </div>
                <div class="item m-b-20rem flex flex-col items-center">
                  <img class="h-65rem" src="@/assets/images/<EMAIL>" alt="">
                  <div class="m-t-30rem text-18rem">
                    {{ data.top3[2] }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </NTabPane>
      <NTabPane :name="2" tab="三产">
        <div class="flex justify-between">
          <!-- card1 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">客流分析</span>
            </div>
            <div class="bd m-t-20rem">
              <div class="flex items-center">
                <img class="h-15rem w-15rem" src="@/assets/images/<EMAIL>" alt="">
                <span class="m-l-10rem text-16rem">客流量</span>
              </div>
              <div class="visitor flex items-center justify-between">
                <div class="item w-50%">
                  <div class="p-t-20rem">
                    <span class="count text-24rem font-bold tracking-tight font-ddin">{{ data.visitor[0] }}</span>
                    <span class="m-l-5rem text-14rem opacity-80">人</span>
                  </div>
                  <div class="text-14rem">
                    本年客流量
                  </div>
                </div>
                <div class="item w-50%">
                  <div class="p-t-20rem">
                    <span class="count text-24rem font-bold tracking-tight font-ddin">{{ data.visitor[1] }}</span>
                    <span class="m-l-5rem text-14rem opacity-80">人</span>
                  </div>
                  <div class="text-14rem">
                    本月客流量
                  </div>
                </div>
              </div>
              <div class="m-t-20rem flex items-center">
                <img class="h-15rem w-15rem" src="@/assets/images/<EMAIL>" alt="">
                <span class="m-l-10rem text-16rem">游客画像</span>
              </div>
              <div class="portrait m-t-10rem flex">
                <div class="item">
                  <div class="p-t-15rem text-14rem">
                    男({{ (data.visitor_face[0] / (data.visitor_face[0] + data.visitor_face[1]) * 100).toFixed(2) }}%)
                  </div>
                  <div class="m-t-5rem">
                    <span class="text-24rem text-#25DCA7 font-bold tracking-tight font-ddin">{{ data.visitor_face[0] }}</span>
                    <span class="m-l-5rem text-14rem opacity-80">人</span>
                  </div>
                </div>
                <div class="item">
                  <div class="p-t-15rem text-14rem">
                    女({{ (data.visitor_face[1] / (data.visitor_face[0] + data.visitor_face[1]) * 100).toFixed(2) }}%)
                  </div>
                  <div class="m-t-5rem">
                    <span class="text-24rem text-#FFAB2B font-bold tracking-tight font-ddin">{{ data.visitor_face[1] }}</span>
                    <span class="m-l-5rem text-14rem opacity-80">人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="blank m-x-20rem m-t-100rem" />
          <!-- card2 -->
          <div class="card w-49%">
            <div class="hd flex items-center">
              <img src="@/assets/images/<EMAIL>">
              <span class="m-l-25rem">热销产品</span>
            </div>
            <div class="bd hot m-t-20rem">
              <div v-for="(item, index) in data.salehot" :key="`hot${index}`" class="item flex items-center justify-between">
                <div class="cover">
                  <img class="h-72rem w-72rem" :src="baseImageUrl + item.cover" alt="">
                </div>
                <div class="info">
                  <div class="text-16rem color-#E6FFF8">
                    {{ item.name }}
                  </div>
                  <div class="flex items-center justify-between">
                    <NProgress
                      :style="`--left: ${item.rate}%`"
                      type="line"
                      :percentage="item.rate"
                      indicator-placement="inside"
                      processing
                    />
                    <span class="m-l-10rem text-24rem color-#25DCA7 font-bold font-ddin">{{ item.rate }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- card3 -->
        <div class="card m-t-30rem">
          <div class="hd flex items-center">
            <img src="@/assets/images/<EMAIL>">
            <span class="m-l-25rem">欢声笑语</span>
          </div>
          <div class="bd happy m-t-20rem">
            <NCarousel class="h-360rem w-100%" autoplay draggable :show-dots="false" show-arrow>
              <div v-for="(list, index) in arrayChunk(data.happy, 6)" :key="`slide${index}`" class="flex flex-wrap items-center gap-10rem">
                <div v-for="(item, i) in list" :key="`cover${i}`" class="cover">
                  <img :src="baseImageUrl + item" alt="">
                </div>
              </div>
            </NCarousel>
          </div>
        </div>
      </NTabPane>
    </NTabs>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: calc(100% - 464rem - 464rem - 80rem);
  height: 936rem;
  left: 504rem;
  bottom: 20rem;
  padding-top: 0;
  .close {
    position: absolute;
    z-index: 99;
    right: 10rem;
    top: 10rem;
  }
}

.blank {
  border-left: 1rem solid rgba(255, 255, 255, 0.3);
  height: 200rem;
}

:deep(.n-tabs-nav) {
  background: url(@/assets/images/<EMAIL>) no-repeat center top;
  background-size: contain;
  height: 90rem;
  padding-top: 15rem;
  .n-tabs-nav-scroll-content {
    justify-content: center;
  }
  .n-tabs-tab {
    display: flex;
    justify-content: center;
    width: 165rem;
    height: 44rem;
    font-size: 24rem;
    color: #fff;
    &:hover,
    &.n-tabs-tab--active {
      color: #fff;
      text-shadow: 0 0 7rem rgba(255, 209, 138, 0.5);
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: contain;
    }
  }
  .n-tabs-tab-pad,
  .n-tabs-bar {
    display: none;
  }
}
.tree,
.product {
  .item {
    margin-top: 20rem;
    .cover {
      width: 108rem;
      border: 1rem solid rgba(37, 220, 167, 0.5);
    }
    .info {
      width: calc(100% - 118rem);
      .title {
        background: url(@/assets/images/<EMAIL>) no-repeat center top;
        background-size: 100% 100%;
        width: 100%;
        height: 36rem;
        line-height: 36rem;
        padding-left: 10rem;
      }
    }
  }
}
.area {
  .total {
    background: url(@/assets/images/<EMAIL>) no-repeat left top;
    background-size: auto 100%;
    width: 100%;
    height: 88rem;
    line-height: 88rem;
    padding: 0 20rem 0 50rem;
  }
}
.scale {
  .item {
    background: url(@/assets/images/<EMAIL>) no-repeat center top;
    background-size: 100% 100%;
    width: 24%;
    height: 246rem;
  }
}
.sale {
  .item {
    width: 49%;
    height: 109rem;
    background: url(@/assets/images/<EMAIL>) no-repeat center top;
    background-size: 100% 100%;
    text-align: center;
    margin-bottom: 20rem;
    &:nth-child(2n) {
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: 100% 100%;
    }
    .title {
      line-height: 44rem;
    }
  }
}
.rank {
  .wrap {
    width: 100%;
    height: 230rem;
    margin: 0 auto;
    background: url(@/assets/images/<EMAIL>) no-repeat center bottom;
    background-size: 372rem auto;
  }
  .item {
    width: 32%;
  }
}
.visitor {
  .item {
    padding-left: 100rem;
    &:nth-child(1) {
      height: 103rem;
      background: url(@/assets/images/<EMAIL>) no-repeat left top;
      background-size: 212rem 103rem;
      .count {
        background: linear-gradient(180deg, #ffffff 0%, #c3ffed 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
    }
    &:nth-child(2) {
      height: 103rem;
      background: url(@/assets/images/<EMAIL>) no-repeat left top;
      background-size: 212rem 103rem;
      .count {
        background: linear-gradient(180deg, #ffffff 0%, #c0fcff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
    }
  }
}
.portrait {
  background: url(@/assets/images/<EMAIL>) no-repeat left top;
  background-size: 422rem 92rem;
  width: 422rem;
  height: 92rem;
  margin-left: 10rem;
  padding-left: 120rem;
  .item {
    width: 152rem;
    padding-left: 20rem;
  }
}
.happy {
  .cover {
    border: 1rem solid rgba(12, 143, 103, 1);
    width: 32.5%;
    img {
      width: 100%;
      height: 170rem;
    }
  }
}
.hot {
  .item {
    padding-top: 15rem;
    .cover {
      width: 72rem;
      border: 1rem solid rgba(37, 220, 167, 0.5);
    }
    .info {
      width: calc(100% - 87rem);
      :deep(.n-progress) {
        .n-progress-graph-line-rail {
          height: 18rem;
          line-height: 18rem;
          border-radius: 0;
          background: url(@/assets/images/<EMAIL>) repeat-x left center;
          background-size: 13rem 18rem;
        }
        .n-progress-graph-line-indicator {
          display: none;
        }
        .n-progress-graph-line-fill {
          border-radius: 0;
          background: url(@/assets/images/<EMAIL>) repeat-x left center;
          background-size: 13rem 18rem;
        }
      }
    }
  }
}
</style>
