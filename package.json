{"type": "module", "private": true, "packageManager": "pnpm@9.9.0", "scripts": {"build": "vite build", "dev": "vite --port 3333", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "vue-tsc --noEmit", "preview": "vite preview", "test": "vitest", "up": "taze major -I", "postinstall": "npx simple-git-hooks"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@vueuse/core": "^11.0.3", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "ezuikit-js": "8.1.1-alpha.2", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "mini3d": "workspace:^1.0.0", "mitt": "^3.0.1", "naive-ui": "^2.40.1", "pinia": "^2.2.5", "pinia-plugin-persistedstate": "^4.1.2", "vue": "^3.5.3", "vue-router": "^4.4.3", "xgplayer": "^3.0.20"}, "devDependencies": {"@antfu/eslint-config": "^3.4.0", "@iconify-json/carbon": "^1.2.1", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^22.5.4", "@unocss/eslint-config": "^0.62.3", "@unocss/eslint-plugin": "^0.62.3", "@unocss/reset": "^0.62.3", "@vitejs/plugin-vue": "^5.1.3", "@vue-macros/volar": "^0.28.1", "@vue/test-utils": "^2.4.6", "eslint": "^9.10.0", "eslint-plugin-format": "^0.1.2", "jsdom": "^25.0.0", "lint-staged": "^15.2.10", "pnpm": "^9.9.0", "sass": "~1.78.0", "simple-git-hooks": "^2.11.1", "taze": "^0.16.7", "terser": "^5.36.0", "typescript": "^5.5.4", "unocss": "^0.62.3", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.4", "unplugin-vue-macros": "^2.11.8", "unplugin-vue-router": "^0.10.7", "vite": "^5.4.3", "vite-plugin-pwa": "^0.21.1", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^2.0.5", "vue-tsc": "^2.1.6"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}