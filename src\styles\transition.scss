.left,
.right,
.top,
.bottom {
  &-leave-active,
  &-enter-active {
    transition: all calc(var(--index) * 0.3s) ease;
  }
  &-enter-active {
    transition-delay: 0.6s;
  }
  &-enter-from,
  &-leave-to {
    opacity: 0;
  }
}

.left {
  &-enter-from,
  &-leave-to {
    transform: translateX(-300rem);
  }
}
.right {
  &-enter-from,
  &-leave-to {
    transform: translateX(300rem);
  }
}
.top {
  &-enter-from,
  &-leave-to {
    transform: translateY(-300rem);
  }
}
.bottom {
  &-enter-from,
  &-leave-to {
    transform: translateY(300rem);
  }
}
