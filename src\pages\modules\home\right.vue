<script setup lang="ts">
import Industry from './components/industry.vue'

defineProps<{
  data: any
}>()

const outputPlay = ref(0)
setInterval(() => {
  outputPlay.value = outputPlay.value = (outputPlay.value + 1) % 6
}, 3000)
</script>

<template>
  <div class="box">
    <!-- card1 -->
    <div class="card">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">三产总值</span>
      </div>
      <div class="bd output m-t-20rem p-y-10rem">
        <div class="flex items-center justify-between">
          <div class="item text-center" :class="outputPlay === 1 ? 'active' : ''">
            <div class="circle" />
            <div class="content">
              <div class="text-18rem font-bold font-ddin">
                {{ data.output[0] }}
              </div>
              <div class="text-14rem opacity-90">
                万元
              </div>
              <div class="m-t-40rem text-16rem">
                一产总值
              </div>
            </div>
          </div>
          <div class="item text-center" :class="outputPlay === 3 ? 'active' : ''">
            <div class="circle" />
            <div class="content">
              <div class="text-18rem font-bold font-ddin">
                {{ data.output[1] }}
              </div>
              <div class="text-14rem opacity-90">
                万元
              </div>
              <div class="m-t-40rem text-16rem">
                二产总值
              </div>
            </div>
          </div>
          <div class="item text-center" :class="outputPlay === 5 ? 'active' : ''">
            <div class="circle" />
            <div class="content">
              <div class="text-18rem font-bold font-ddin">
                {{ data.output[2] }}
              </div>
              <div class="text-14rem opacity-90">
                万元
              </div>
              <div class="m-t-40rem text-16rem">
                三产总值
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- card2 -->
    <div class="card m-t-20rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">产业园情况</span>
      </div>
      <div class="bd area m-t-20rem">
        <div class="text-right text-14rem opacity-90">
          单位：亩
        </div>
        <div class="flex items-center justify-between">
          <div class="chart w-60%">
            <Industry :data="data.area" />
          </div>
          <div class="w-40%">
            <div class="flex flex-wrap">
              <div class="w-50% b-l-5rem b-l-#25DCA7 p-l-10rem lh-17rem">
                <div class="text-18rem font-bold font-ddin">
                  {{ data.area[0] }}
                </div>
                <div class="m-t-2rem text-14rem opacity-90">
                  桑田
                </div>
              </div>
              <div class="w-50% b-l-5rem b-l-#FFAB2B p-l-10rem lh-17rem">
                <div class="text-18rem font-bold font-ddin">
                  {{ data.area[1] }}
                </div>
                <div class="m-t-2rem text-14rem opacity-90">
                  一厂
                </div>
              </div>
            </div>
            <div class="blank m-y-35rem" />
            <div class="flex flex-wrap">
              <div class="w-50% b-l-5rem b-l-#25D4DC p-l-10rem lh-17rem">
                <div class="text-18rem font-bold font-ddin">
                  {{ data.area[2] }}
                </div>
                <div class="m-t-2rem text-14rem opacity-90">
                  二厂
                </div>
              </div>
              <div class="w-50% b-l-5rem b-l-#FFFFFF p-l-10rem lh-17rem">
                <div class="text-18rem font-bold font-ddin">
                  {{ data.area[3] }}
                </div>
                <div class="m-t-2rem text-14rem opacity-90">
                  三厂
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- card3 -->
    <div class="card m-t-20rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">群众收入</span>
      </div>
      <div class="bd income m-t-20rem">
        <div class="people p-l-145rem p-t-22rem">
          <div class="flex items-center text-16rem font-ddin">
            <span>带动人民人数</span>
            <template v-if="data.income[0].rate > 0">
              <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
              <span class="color-#25DCA7">{{ data.income[0].rate }}%</span>
            </template>
            <template v-else>
              <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
              <span class="color-#FFAB2B">{{ data.income[0].rate }}%</span>
            </template>
          </div>
          <div>
            <span class="text-28rem text-white font-ddin">{{ data.income[0].value }}</span>
            <span class="m-l-5rem text-14rem color-#E6FFF8 opacity-80">人</span>
          </div>
        </div>
        <div class="money m-t-20rem p-l-145rem p-t-22rem">
          <div class="flex items-center text-16rem">
            <span>带动人民收入</span>
            <template v-if="data.income[1].rate > 0">
              <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
              <span class="color-#25DCA7">{{ data.income[1].rate }}%</span>
            </template>
            <template v-else>
              <img class="m-l-10rem m-r-5rem h-18rem" src="@/assets/images/<EMAIL>">
              <span class="color-#FFAB2B">{{ data.income[1].rate }}%</span>
            </template>
          </div>
          <div>
            <span class="text-28rem text-white font-ddin">{{ data.income[1].value }}</span>
            <span class="m-l-5rem text-14rem color-#E6FFF8 opacity-80">万元</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: 464rem;
  height: 936rem;
  right: 20rem;
  bottom: 20rem;
}
.output {
  .item {
    width: 134rem;
    .circle {
      width: 134rem;
      height: 134rem;
      transition: all 1s ease;
    }
    &.active {
      .circle {
        rotate: -360deg;
      }
    }
    &:nth-child(1) .circle {
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: contain;
    }
    &:nth-child(2) .circle {
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: contain;
    }
    &:nth-child(3) .circle {
      background: url(@/assets/images/<EMAIL>) no-repeat center top;
      background-size: contain;
    }
    .content {
      position: relative;
      margin-top: -88rem;
    }
  }
}
.area {
  .blank {
    width: 100%;
    height: 1rem;
    background:
      linear-gradient(#fff, #fff) left top/10rem 1rem no-repeat,
      rgba(255, 255, 255, 0.2);
  }
}
.income {
  .people,
  .money {
    width: 100%;
    height: 102rem;
    background: url(@/assets/images/<EMAIL>) no-repeat center top;
    background-size: contain;
    .font-ddin {
      background: linear-gradient(180deg, #ffffff 0%, #c0fcff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }
  .money {
    background: url(@/assets/images/<EMAIL>) no-repeat center top;
    background-size: contain;
    .font-ddin {
      background: linear-gradient(180deg, #ffffff 0%, #c3ffed 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }
}
</style>
