import dayjs from 'dayjs'
import Cookies from 'js-cookie'
import { acceptHMRUpdate, defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

interface userInter {
  uid: number
  username: string
  nickname: string
  group: number
}

export const useAuthStore = defineStore(
  'auth',
  () => {
    // 路由
    const router = useRouter()

    // 登录token
    const token = ref(Cookies.get('token') || '')

    // 用户信息
    const userinfo = ref<userInter>()

    // 保存token
    function setToken(param: string) {
      token.value = param
      Cookies.set('token', param, { expires: dayjs().valueOf() + 1000 * 60 * 60 * 7 })
    }

    // 格式化token
    function getToken() {
      return token.value ? `Bearer ${token.value}` : null
    }

    // 清除token
    function clearToken() {
      Cookies.remove('token')
      token.value = ''
      router.replace('/login')
    }

    return {
      userinfo,
      setToken,
      getToken,
      clearToken,
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: ['token'],
    },
  },
)

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useAuthStore as any, import.meta.hot))
