import type {
  BarSeriesOption,
  GaugeSeriesOption,
  LineSeriesOption,
  PictorialBarSeriesOption,
  PieSeriesOption,
  RadarSeriesOption,
  ScatterSeriesOption,
} from 'echarts/charts'
import type {
  DatasetComponentOption,
  GridComponentOption,
  LegendComponentOption,
  TitleComponentOption,
  ToolboxComponentOption,
  TooltipComponentOption,
} from 'echarts/components'
import { useElementSize } from '@vueuse/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ctorial<PERSON>ar<PERSON><PERSON>, <PERSON><PERSON><PERSON>, RadarChart, Scatter<PERSON><PERSON> } from 'echarts/charts'
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  TransformComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { effectScope, nextTick, onScopeDispose, ref, watch } from 'vue'

export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | ScatterSeriesOption
  | PictorialBarSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | TitleComponentOption
  | LegendComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | ToolboxComponentOption
  | DatasetComponentOption
>

echarts.use([
  TitleComponent,
  LegendComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  PictorialBarChart,
  RadarChart,
  GaugeChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
])

interface ChartHooks {
  onRender?: (chart: echarts.ECharts) => void | Promise<void>
  onUpdated?: (chart: echarts.ECharts) => void | Promise<void>
  onDestroy?: (chart: echarts.ECharts) => void | Promise<void>
}

/**
 * use echarts
 *
 * @param optionsFactory echarts options factory function
 */
export function useEcharts<T extends ECOption>(optionsFactory: () => T, hooks: ChartHooks = {}) {
  const scope = effectScope()

  const darkMode = ref(false)

  const domRef = ref<HTMLElement | null>(null)
  const initialSize = { width: 0, height: 0 }
  // @ts-ignore
  const { width, height } = useElementSize(domRef, initialSize)

  let chart: echarts.ECharts | null = null
  const chartOptions: T = optionsFactory()

  const {
    onRender = (instance) => {
      const textColor = 'rgb(224, 224, 224)'
      const maskColor = 'transparent'

      instance.showLoading({
        color: 'rgba(12, 143, 103, 0.8)',
        textColor,
        fontSize: 14,
        maskColor,
      })
    },
    onUpdated = (instance) => {
      instance.hideLoading()
    },
    onDestroy,
  } = hooks

  /**
   * whether can render chart
   *
   * when domRef is ready and initialSize is valid
   */
  function canRender() {
    return domRef.value && initialSize.width > 0 && initialSize.height > 0
  }

  /** is chart rendered */
  function isRendered() {
    return Boolean(domRef.value && chart)
  }

  /**
   * update chart options
   *
   * @param callback callback function
   */
  async function updateOptions(callback: (opts: T, optsFactory: () => T) => ECOption = () => chartOptions) {
    if (!isRendered())
      return

    const updatedOpts = callback(chartOptions, optionsFactory)

    Object.assign(chartOptions, updatedOpts)

    if (isRendered()) {
      chart?.clear()
    }

    chart?.setOption({ ...updatedOpts, backgroundColor: 'transparent' })

    await onUpdated?.(chart!)
  }

  function setOptions(options: T) {
    chart?.setOption(options)
  }

  function autoPlay(length: number, ms: number = 3000, series: boolean = false) {
    let currentIndex = -1
    setInterval(() => {
      // 更新索引
      currentIndex = (currentIndex + 1) % length
      // 显示 Tooltip
      chart?.dispatchAction({
        type: 'showTip',
        seriesIndex: series ? currentIndex : 0, // 系列索引
        dataIndex: series ? 0 : currentIndex, // 数据索引
      })
    }, ms)
  }

  /** render chart */
  async function render() {
    if (!isRendered()) {
      const chartTheme = darkMode.value ? 'dark' : 'light'

      await nextTick()

      chart = echarts.init(domRef.value, chartTheme)

      chart.setOption({ ...chartOptions, backgroundColor: 'transparent' })

      await onRender?.(chart)
    }
  }

  /** resize chart */
  function resize() {
    chart?.resize()
  }

  /** destroy chart */
  async function destroy() {
    if (!chart)
      return

    await onDestroy?.(chart)
    chart?.dispose()
    chart = null
  }

  /** change chart theme */
  async function changeTheme() {
    await destroy()
    await render()
    await onUpdated?.(chart!)
  }

  /**
   * render chart by size
   *
   * @param w width
   * @param h height
   */
  async function renderChartBySize(w: number, h: number) {
    initialSize.width = w
    initialSize.height = h

    // size is abnormal, destroy chart
    if (!canRender()) {
      await destroy()

      return
    }

    // resize chart
    if (isRendered()) {
      resize()
    }

    // render chart
    await render()
  }

  scope.run(() => {
    watch([width, height], ([newWidth, newHeight]) => {
      // @ts-ignore
      renderChartBySize(newWidth, newHeight)
    })

    watch(darkMode, () => {
      changeTheme()
    })
  })

  onScopeDispose(() => {
    destroy()
    scope.stop()
  })

  return {
    domRef,
    updateOptions,
    setOptions,
    autoPlay,
  }
}
