<script setup>
import emitter from '@/utils/mitt'
import gsap from 'gsap'
import { World } from './factory.js'

// 世界容器
const canvasRef = ref(null)

// 世界对象
const world = ref(null)

// 组列表
const groupList = ref(['普益茶', '茧丝绸', '富莎厂'])

// 当前组
const group = ref(0)

onMounted(() => {
  emitter.on('loadFactory', load)
})

onBeforeUnmount(() => {
  world.value.config.hasUpdate = false
  world.value && world.value.destroy()
})

// 加载地图
function load(assets) {
  world.value = new World(canvasRef.value, assets)
  world.value.config.hasUpdate = false
}

// 入场动画
async function play() {
  world.value.config.hasUpdate = true
  world.value.animateTl.timeScale(1) // 设置播放速度正常
  world.value.animateTl.play()
  showBtn()
}

// 显示按钮
function showBtn() {
  gsap.to('.factory .control .btn', { y: 0, stagger: 0.1 })
}

// 隐藏按钮
async function hideBtn() {
  gsap.to('.factory .control .btn', { y: '200%', stagger: 0.1 })
}

// 切换组
function changeGroup(index) {
  group.value = group.value === index + 1 ? 0 : index + 1
  world.value.flyTo(group.value)
  // 显隐左右信息
  emitter.emit('leftRightVisible', group.value === 0)
  // 显隐传感器信息框
  if (group.value === 0) {
    emitter.emit('sensor', null)
  }
}

watch(
  () => group.value,
  (nval, oval) => {
    switch (oval) {
      case 0:
        if (nval !== 2)
          world.value.factoryB.visible = false
        break
      case 1:
        world.value.factoryA.visible = false
        break
      case 2:
        if (nval !== 0)
          world.value.factoryB.visible = false
        break
      case 3:
        world.value.factoryC.visible = false
        break
      default:
        break
    }
  },
)

// 暴露给父组件调用
defineExpose({ world, play, group })
</script>

<template>
  <div class="world factory">
    <canvas ref="canvasRef" />
    <div class="control">
      <div v-for="(item, index) in groupList" :key="index" class="btn" :class="{ active: group === index + 1 }" @click="changeGroup(index)">
        {{ item }}
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.world.factory {
  canvas {
    filter: saturate(1.1) brightness(1.1) contrast(1.1);
  }
  .sensor-label {
    display: flex;
    opacity: 0;
    transform: translateY(200%);
    will-change: transform;
    &.reverse {
      flex-direction: row-reverse;
      .icon {
        transform: scaleX(-1);
      }
      .name {
        padding: 0 15rem 0 10rem;
        border-radius: 3rem 0 0 3rem;
        border-left-width: 1rem;
        border-right-width: 0;
        margin-left: 0;
        margin-right: -5rem;
      }
    }
    &.show {
      pointer-events: auto;
      cursor: pointer;
    }
    .icon {
      display: block;
      line-height: 1;
      width: 32rem;
      height: 37rem;
      position: relative;
    }
    .name {
      background: rgba(0, 0, 0, 0.6);
      border: 1rem solid #0c9ca3;
      border-left-width: 0;
      border-radius: 0 3rem 3rem 0;
      height: 32rem;
      line-height: 30rem;
      padding: 0 10rem 0 15rem;
      font-size: 12rem;
      margin-left: -5rem;
      color: #fff;
      &.type-1 {
        border-color: #0c9ca3;
      }
      &.type-2 {
        border-color: #a66a0e;
      }
      &.type-3 {
        border-color: #0c8f67;
      }
      &.type-4 {
        border-color: #17af51;
      }
      &.type-5 {
        border-color: #a6430e;
      }
      &.type-6 {
        border-color: #0a6e97;
      }
    }
  }
}
</style>
