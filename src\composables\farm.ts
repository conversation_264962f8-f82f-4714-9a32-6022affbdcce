// 视角模式
export const modeList = [
  {
    name: '默认',
    camera: {
      position: [-90, 30, 0],
      controls: [-60, 0, -65],
    },
  },
  {
    name: '网格',
    camera: {
      position: [-140, 400, 280],
      controls: [-140, 0, 110],
    },
  },
  {
    name: '建筑',
    camera: {
      position: [-20, 50, 40],
      controls: [-20, 0, -40],
    },
  },
  {
    name: '传感器',
    camera: {
      position: [-130, 90, 10],
      controls: [-40, 0, -80],
    },
  },
  {
    name: '电表',
    camera: {
      position: [-20, 50, 40],
      controls: [-20, 0, -40],
    },
  },
]

// 网格
export const gridList = [
  {
    type: 1,
    title: '网格一',
    icon: './farm/icon/grid.svg',
    position: [0, 0, -130],
    camera: {
      position: [-90, 50, 0],
      controls: [-60, 0, -65],
    },
    vertices: [-198, 0, -232, 150, 0, -232, 148, 0, -26, -124, 0, -32],
    indices: [0, 1, 2, 0, 2, 3],
    color: 0xF08C00,
    extra: {
      area: '90.719',
      name: '红果二号',
      leader: '翟先根',
    },
  },
  {
    type: 1,
    title: '网格二',
    icon: './farm/icon/grid.svg',
    position: [350, 0, -130],
    camera: {
      position: [260, 50, 0],
      controls: [290, 0, -65],
    },
    vertices: [166, 0, -232, 588, 0, -232, 588, 0, -19, 163, 0, -26],
    indices: [0, 1, 2, 0, 2, 3],
    color: 0xE8590C,
    extra: {
      area: '128.025',
      name: '桑71-1',
      leader: '王东',
    },
  },
  {
    type: 1,
    title: '网格三',
    icon: './farm/icon/grid.svg',
    position: [350, 0, 100],
    camera: {
      position: [260, 50, -100],
      controls: [290, 0, 35],
    },
    vertices: [163, 0, -12, 588, 0, -5, 588, 0, 177, 160, 0, 177, 200, 0, 177, 200, 0, 228, 588, 0, 230],
    indices: [0, 1, 2, 0, 2, 3, 2, 4, 5, 2, 5, 6],
    color: 0x3B5BDB,
    extra: {
      area: '153.859',
      name: '桑71-1',
      leader: '翟先根',
    },
  },
  {
    type: 1,
    title: '网格四',
    icon: './farm/icon/grid.svg',
    position: [50, 0, 100],
    camera: {
      position: [-20, 50, -120],
      controls: [10, 0, 35],
    },
    vertices: [-117, 0, -18, 148, 0, -12, 143, 0, 177, 85, 0, 180, 14, 0, 201],
    indices: [0, 1, 2, 0, 2, 3, 0, 3, 4],
    color: 0x099268,
    extra: {
      area: '68.268',
      name: '桑71-1',
      leader: '王东',
    },
  },
  {
    type: 1,
    title: '网格五',
    icon: './farm/icon/grid.svg',
    position: [-400, 0, 0],
    camera: {
      position: [-40, 100, 30],
      controls: [-270, 0, 80],
    },
    vertices: [-583, 0, -232, -215, 0, -232, -132, 0, -18, -583, 0, 148, -565, 0, 205, 2, 0, 205],
    indices: [0, 1, 2, 0, 2, 3, 2, 3, 4, 2, 4, 5],
    color: 0xE03131,
    extra: {
      area: '325.28',
      name: '桑71-1',
      leader: '翟先根',
    },
  },
]

// 建筑物
export const houseList = [
  {
    type: 2,
    title: '蚕趣馆',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [10, 0, -140],
    camera: {
      position: [90, 20, -140],
      controls: [10, 0, -140],
    },
    extra: {
      cover: './upload/house4.jpg',
      area: '150平方米',
      sensor: 1,
      desc: '蚕趣馆是一个全年开放的养蚕观光景点，旨在向公众展示蚕的生命周期和养蚕文化。馆内设有专门的养蚕区，游客可以在这里观察到蚕从卵到幼虫、蛹再到成虫的全过程，了解蚕的生长习性和养蚕技术。此外，蚕趣馆还通过图文并茂的展板、互动体验区和定期的养蚕工作坊，让游客亲身体验养蚕的乐趣，学习蚕丝的制作工艺，从而加深对蚕桑文化的认识和兴趣。通过四季不断的养蚕活动，蚕趣馆成为了连接自然、文化和教育的桥梁。',
    },
  },
  {
    type: 2,
    title: '蚕桑文化馆',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [20, 0, -125],
    camera: {
      position: [20, 20, -45],
      controls: [20, 0, -125],
    },
    extra: {
      cover: './upload/house5.jpg',
      area: '350平方米',
      sensor: 1,
      desc: '蚕桑文化馆是一个专注于展示桑蚕茧丝文化及其相关产品的场所，通过四方位的展示，全面介绍蚕桑产业的历史、技术、文化和经济价值。馆内设有专门的展区，分别展示桑树种植、蚕的养殖、茧的加工和丝的织造等环节，让游客深入了解从桑叶到丝绸的整个生产过程。通过丰富的图文资料、实物展示和互动体验，蚕桑文化馆不仅向公众传递了蚕桑产业的知识，还弘扬了与之相关的传统工艺和文化内涵，成为连接过去与未来、传统与现代的桥梁。',
    },
  },
  {
    type: 2,
    title: '观桑亭',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [-15, 0, -120],
    camera: {
      position: [-15, 20, -40],
      controls: [-15, 0, -120],
    },
    extra: {
      cover: './upload/house3.jpg',
      area: '104平方米',
      sensor: 0,
      desc: '观桑亭是一座以蚕茧为设计灵感的观景亭，位于桑田之中，为游客提供了一个登高远眺的绝佳位置。亭子的造型独特，仿佛一个巨大的蚕茧，既体现了与蚕桑文化的紧密联系，又为田园风光增添了一道亮丽的风景线。站在亭中，游客可以俯瞰四周的阡陌交错，欣赏到桑园的全貌，感受大自然的宁静与美丽。观桑亭不仅是观赏田园风光的平台，也是体验和学习蚕桑文化的重要场所。',
    },
  },
  {
    type: 2,
    title: '果桑科技馆',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [80, 0, -206],
    camera: {
      position: [120, 50, -126],
      controls: [80, 0, -206],
    },
    extra: {
      cover: './upload/house7.jpg',
      area: '2760平方米',
      sensor: 2,
      desc: '果桑科技馆是一个结合现代科技与传统蚕桑养殖的展示场所，旨在向公众展示如何利用先进技术提高蚕桑养殖的效率和质量。馆内设有先进的自动化养殖设备，如智能温湿度控制系统、自动喂食机和蚕茧加工机械，这些设备能够精确控制养殖环境，提高蚕的生长速度和茧丝的质量。此外，科技馆还通过互动式展览和多媒体演示，向游客介绍蚕桑养殖的科学原理、现代养殖技术的应用以及可持续发展的理念。通过参观果桑科技馆，游客可以了解到蚕桑产业的现代化进程和未来发展趋势。',
    },
  },
  {
    type: 2,
    title: '桑蚕制品深加工展示馆',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [-50, 0, -130],
    camera: {
      position: [-50, 20, -50],
      controls: [-50, 0, -130],
    },
    extra: {
      cover: './upload/house2.jpg',
      area: '522平方米',
      sensor: 1,
      desc: '桑蚕制品深加工展示馆是一个专注于展示蚕桑产品从原材料到成品的加工过程的场所。游客在这里可以亲眼目睹从桑叶到蚕丝，再到最终丝绸制品的整个生产流程。展示馆通过现代化的展示手段，如互动式展览、视频演示和现场解说，详细介绍了蚕桑的养殖技术、蚕丝的提取、织造技术以及丝绸的染色和后处理等环节。通过这种生动的展示方式，游客不仅能够学习到蚕桑制品的加工知识，还能领略到传统工艺与现代科技相结合的魅力。',
    },
  },
  {
    type: 2,
    title: '桑蚕制品体验馆',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [-95, 0, -80],
    camera: {
      position: [-15, 20, -80],
      controls: [-95, 0, -80],
    },
    extra: {
      cover: './upload/house1.jpg',
      area: '1284平方米',
      sensor: 1,
      desc: '桑蚕制品体验馆是一个结合了蚕桑农家乐和商品展示销售的场所。在这里，游客可以亲身体验从桑叶采摘到蚕宝宝喂养的全过程，感受蚕桑文化的魅力。体验馆内设有商品展示区，陈列着由蚕丝制成的各种产品，如丝绸服装、床上用品、装饰品等，让游客直观了解蚕丝产品的制作工艺和品质。此外，体验馆还提供蚕桑知识讲解，让游客在享受农家乐的同时，学习到关于蚕桑的科学知识和文化历史。通过这种互动体验，游客能够更加深入地了解和欣赏中国传统的蚕桑文化。',
    },
  },
  {
    type: 2,
    title: '洗手间',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [11, 0, -60],
    camera: {
      position: [71, 20, -60],
      controls: [11, 0, -60],
    },
    extra: {
      cover: './upload/house8.jpg',
      area: '100平方米',
      sensor: 1,
      desc: '智能桑树灌溉设备是一种高科技农业灌溉系统，它利用传感器、控制器和网络技术实现对桑树灌溉的精确管理。该设备能够实时监测土壤湿度、温度和桑树生长状况，通过数据分析，自动调节灌溉量和频率，确保桑树得到最适宜的水分供应。智能灌溉系统不仅提高了水资源的利用效率，减少了水浪费，还能通过优化桑树的生长环境，提升桑叶的品质和产量。此外，该系统通常配备远程控制功能，使得农户可以远程监控和调整灌溉计划，极大地提高了农业生产的便捷性和智能化水平。',
    },
  },
  {
    type: 2,
    title: '游客服务中心',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [80, 0, -75],
    camera: {
      position: [80, 20, 5],
      controls: [80, 0, -75],
    },
    extra: {
      cover: './upload/house6.jpg',
      area: '860平方米',
      sensor: 1,
      desc: '游客服务中心是旅游景点的重要组成部分，主要负责接待游客并提供全面的服务。该中心通常设有咨询台，为游客提供景点信息、路线规划、住宿推荐等咨询服务。此外，服务中心还可能提供票务服务、旅游商品销售、紧急救援、失物招领等服务，确保游客在景点的体验更加便捷和安全。服务中心的工作人员通常训练有素，能够用多种语言与游客沟通，满足不同游客的需求。通过提供高效、友好的服务，游客服务中心有助于提升游客的整体满意度，增强旅游体验。',
    },
  },
  {
    type: 2,
    title: '智能蚕室',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [540, 0, 70],
    camera: {
      position: [540, 20, -10],
      controls: [540, 0, 70],
    },
    extra: {
      cover: '',
      area: '3780平方米',
      sensor: 6,
      desc: '智能蚕室是利用现代化技术进行蚕的养殖和管理的场所。它通过集成先进的传感器、自动化控制系统和数据分析工具，实现对蚕室环境的精确控制。这些设备能够监测和调节温度、湿度、光照等关键参数，确保蚕在最佳环境中生长。智能蚕室还可能配备自动喂食系统、废物处理系统和病虫害监控系统，以提高养蚕效率和蚕丝产量。通过这种方式，智能蚕室不仅提升了蚕丝产品的质量和产量，还减少了人工成本和资源浪费，推动了蚕桑产业的现代化发展。',
    },
  },
  {
    type: 2,
    title: '智能灌溉中心',
    icon: './farm/icon/house.svg',
    color: '#89A4EA',
    position: [7, 0, -70],
    camera: {
      position: [67, 20, -70],
      controls: [7, 0, -70],
    },
    extra: {
      cover: '',
      area: '35平方米',
      sensor: 1,
      desc: '智能桑树灌溉设备是一种高科技农业灌溉系统，它利用传感器、控制器和网络技术实现对桑树灌溉的精确管理。该设备能够实时监测土壤湿度、温度和桑树生长状况，通过数据分析，自动调节灌溉量和频率，确保桑树得到最适宜的水分供应。智能灌溉系统不仅提高了水资源的利用效率，减少了水浪费，还能通过优化桑树的生长环境，提升桑叶的品质和产量。此外，该系统通常配备远程控制功能，使得农户可以远程监控和调整灌溉计划，极大地提高了农业生产的便捷性和智能化水平。',
    },
  },
]

// 传感器
export const sensorList = [
  {
    type: 31,
    title: '绿能病虫害监测站',
    icon: './farm/icon/sensor_1.svg',
    color: '#F7DA89',
    position: [60, 0, -15],
    camera: {
      position: [60, 20, 65],
      controls: [60, 0, -15],
    },
    extra: {
      imei: '0701240003',
      cover: './upload/insect1.png',
      code: 'SN1652123456',
      time: '2024-05-12 19:12:34',
      num: 13,
      insect: '小灰蛾、蛾蠓、蝽',
    },
  },
  {
    type: 32,
    title: '摄像头1',
    icon: './farm/icon/sensor_2.svg',
    color: '#62D8DB',
    position: [-103, 0, -17],
    camera: {
      position: [-103, 50, 63],
      controls: [-103, 0, -17],
    },
    extra: {
      imei: 'AW7654734_1',
      url: 'ezopen://open.ys7.com/AW7654734/1.live',
    },
  },
  {
    type: 32,
    title: '摄像头2',
    icon: './farm/icon/sensor_2.svg',
    color: '#62D8DB',
    position: [-1.5, 0, -16],
    camera: {
      position: [-1.5, 50, 64],
      controls: [-1.5, 0, -16],
    },
    extra: {
      imei: 'AW7654734_2',
      url: 'ezopen://open.ys7.com/AW7654734/2.live',
    },
  },
  {
    type: 32,
    title: '摄像头3',
    icon: './farm/icon/sensor_2.svg',
    color: '#62D8DB',
    position: [195, 0, -13],
    camera: {
      position: [115, 50, 67],
      controls: [195, 0, -13],
    },
    extra: {
      imei: 'AW7654734_3',
      url: 'ezopen://open.ys7.com/AW7654734/3.live',
    },
  },
  {
    type: 32,
    title: '摄像头4',
    icon: './farm/icon/sensor_2.svg',
    color: '#62D8DB',
    position: [304, 0, -11],
    camera: {
      position: [224, 50, -91],
      controls: [304, 0, -11],
    },
    extra: {
      imei: 'AW7654734_4',
      url: 'ezopen://open.ys7.com/AW7654734/4.live',
    },
  },
  {
    type: 32,
    title: '摄像头5',
    icon: './farm/icon/sensor_2.svg',
    color: '#62D8DB',
    position: [406, 0, -9],
    camera: {
      position: [326, 50, -89],
      controls: [406, 0, -9],
    },
    extra: {
      imei: 'AW7654734_5',
      url: 'ezopen://open.ys7.com/AW7654734/5.live',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯1',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [28, 0, -16],
    camera: {
      position: [-52, 50, 64],
      controls: [28, 0, -16],
    },
    extra: {
      imei: '21084806',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯2',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [100, 0, -13],
    camera: {
      position: [20, 50, 67],
      controls: [100, 0, -13],
    },
    extra: {
      imei: '21084803',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯3',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [170, 0, -150],
    camera: {
      position: [90, 50, -70],
      controls: [170, 0, -150],
    },
    extra: {
      imei: '21084804',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯4',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [170, 0, -100],
    camera: {
      position: [90, 50, -20],
      controls: [170, 0, -100],
    },
    extra: {
      imei: '21084805',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯5',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [170, 0, -50],
    camera: {
      position: [90, 50, 30],
      controls: [170, 0, -50],
    },
    extra: {
      imei: '21084807',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯6',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [170, 0, 0],
    camera: {
      position: [90, 50, 80],
      controls: [170, 0, 0],
    },
    extra: {
      imei: '21084808',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯7',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [170, 0, 50],
    camera: {
      position: [90, 50, -30],
      controls: [170, 0, 50],
    },
    extra: {
      imei: '21084809',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 33,
    title: '太阳能诱捕灯8',
    icon: './farm/icon/sensor_3.svg',
    color: '#6FC7EB',
    position: [170, 0, 100],
    camera: {
      position: [90, 50, 20],
      controls: [170, 0, 100],
    },
    extra: {
      imei: '21084810',
      rain: '无雨',
      light: '白天',
      guide: '关闭',
      wing: '关闭',
    },
  },
  {
    type: 34,
    title: '环境监测点1',
    icon: './farm/icon/sensor_4.svg',
    color: '#E68D30',
    position: [-20, 0, 0],
    camera: {
      position: [-100, 50, 80],
      controls: [-20, 0, 0],
    },
    extra: {
      imei: '40311696',
      title: '空气检测仪4',
      temp: '32',
      weight: '13',
      pm: '6.7',
      co2: '12',
      so2: '34',
      pa: '12',
    },
  },
  {
    type: 34,
    title: '环境监测点2',
    icon: './farm/icon/sensor_4.svg',
    color: '#E68D30',
    position: [52, 0, -100],
    camera: {
      position: [-28, 50, -20],
      controls: [52, 0, -100],
    },
    extra: {
      imei: '40311669',
      title: '空气检测仪4',
      temp: '32',
      weight: '13',
      pm: '6.7',
      co2: '12',
      so2: '34',
      pa: '12',
    },
  },
  {
    type: 34,
    title: '环境监测点3',
    icon: './farm/icon/sensor_4.svg',
    color: '#E68D30',
    position: [180, 0, -30],
    camera: {
      position: [100, 50, 50],
      controls: [180, 0, -30],
    },
    extra: {
      imei: '40311674',
      title: '空气检测仪4',
      temp: '32',
      weight: '13',
      pm: '6.7',
      co2: '12',
      so2: '34',
      pa: '12',
    },
  },
  {
    type: 34,
    title: '环境监测点4',
    icon: './farm/icon/sensor_4.svg',
    color: '#E68D30',
    position: [130, 0, 95],
    camera: {
      position: [80, 50, 15],
      controls: [130, 0, 95],
    },
    extra: {
      imei: '40311677',
      title: '空气检测仪4',
      temp: '32',
      weight: '13',
      pm: '6.7',
      co2: '12',
      so2: '34',
      pa: '12',
    },
  },
]

// 电表
export const meterList = [
  {
    type: 4,
    title: '能碳终端1',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [-90, 0, -90],
    camera: {
      position: [-10, 50, -90],
      controls: [-90, 0, -90],
    },
    extra: {
      imei: '863569067931063',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端2',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [-50, 0, -130],
    camera: {
      position: [-50, 50, -50],
      controls: [-50, 0, -130],
    },
    extra: {
      imei: '863569067908202',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端3',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [11, 0, -60],
    camera: {
      position: [91, 50, -60],
      controls: [11, 0, -60],
    },
    extra: {
      imei: '863569067931162',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端4',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [8, 0, -125],
    camera: {
      position: [8, 50, -45],
      controls: [8, 0, -125],
    },
    extra: {
      imei: '863569067920975',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端5',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [20, 0, -135],
    camera: {
      position: [100, 50, -135],
      controls: [20, 0, -135],
    },
    extra: {
      imei: '863569067907543',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端6',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [25, 0, -190],
    camera: {
      position: [25, 50, -110],
      controls: [25, 0, -190],
    },
    extra: {
      imei: '863569067931204',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端7',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [80, 0, -180],
    camera: {
      position: [80, 50, -100],
      controls: [80, 0, -180],
    },
    extra: {
      imei: '863569067907527',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端8',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [80, 0, -75],
    camera: {
      position: [80, 50, 5],
      controls: [80, 0, -75],
    },
    extra: {
      imei: '864708061770908',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端9',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [110, 0, -60],
    camera: {
      position: [110, 50, 20],
      controls: [110, 0, -60],
    },
    extra: {
      imei: '863569067954685',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端10',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [452, 0, 65],
    camera: {
      position: [372, 50, -15],
      controls: [452, 0, 65],
    },
    extra: {
      imei: '863569067931139',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端11',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [480, 0, 65],
    camera: {
      position: [400, 50, -15],
      controls: [480, 0, 65],
    },
    extra: {
      imei: '863569067907709',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端12',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [508, 0, 65],
    camera: {
      position: [428, 50, -15],
      controls: [508, 0, 65],
    },
    extra: {
      imei: '863569067932467',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端13',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [533, 0, 65],
    camera: {
      position: [453, 50, -15],
      controls: [533, 0, 65],
    },
    extra: {
      imei: '863569067931220',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端14',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [560, 0, 65],
    camera: {
      position: [480, 50, -15],
      controls: [560, 0, 65],
    },
    extra: {
      imei: '863569067921221',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
  {
    type: 4,
    title: '能碳终端15',
    icon: './farm/icon/meter.svg',
    color: '#0CA678',
    position: [587, 0, 65],
    camera: {
      position: [507, 50, -15],
      controls: [587, 0, 65],
    },
    extra: {
      imei: '863569067908319',
      total: '1.8',
      factor: '1.0',
      w: '282.0',
      an: '1.2',
      v: '227.4',
    },
  },
]
