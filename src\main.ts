import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { registerSW } from 'virtual:pwa-register'
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { routes } from 'vue-router/auto-routes'
import App from './App.vue'

import 'virtual:svg-icons-register'
import '@unocss/reset/tailwind.css'
import 'uno.css'

registerSW({ immediate: true })

const app = createApp(App)
const router = createRouter({
  routes,
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
})
app.use(router)

const pinia = createPinia().use(piniaPluginPersistedstate)
app.use(pinia)

app.mount('#app')
