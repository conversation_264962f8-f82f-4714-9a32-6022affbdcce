/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/AppProvider.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NCarousel: typeof import('naive-ui')['NCarousel']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NInput: typeof import('naive-ui')['NInput']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NProgress: typeof import('naive-ui')['NProgress']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./../components/SvgIcon.vue')['default']
    TheCounter: typeof import('./../components/TheCounter.vue')['default']
    TheInput: typeof import('./../components/TheInput.vue')['default']
  }
}
