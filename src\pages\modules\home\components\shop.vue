<script lang="ts" setup>
import { useEcharts } from '@/hooks/echarts'
import { sleep } from '@/utils'

const props = defineProps<{
  data: any
}>()

const { domRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    backgroundColor: 'rgba(0,0,0,.6)',
    borderColor: 'rgba(12, 143, 103, 1)',
    padding: 8,
    textStyle: {
      color: 'rgba(255,255,255,.9)',
      fontSize: 12,
    },
  },
  radar: {
    shape: 'circle',
    radius: '80%',
    center: ['50%', '50%'],
    startAngle: 0,
    triggerEvent: true,
    axisName: {
      color: '#fff',
      fontSize: '14',
      borderRadius: 3,
      padding: 20,
    },
    axisNameGap: '2',
    indicator: [{}] as { name: string, max: number }[],
    alignTicks: false, // 关闭对齐
    splitArea: {
      areaStyle: {
        color: [
          'rgba(0,255,255, 0.1)',
          'rgba(0,255,255, 0.2)',
          'rgba(0,255,255, 0.3)',
          'rgba(0,255,255, 0.4)',
          'rgba(0,255,255, 0.5)',
          'rgba(0,255,255, 0.6)',
        ].reverse(),
        shadowColor: 'rgba(0, 0, 0, 1)',
        shadowBlur: 30,
        shadowOffsetX: 10,
        shadowOffsetY: 10,
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(0,206,209, 0.3)',
      },
    },
    splitLine: {
      lineStyle: {
        width: 1,
        color: [
          'rgba(0,206,209, 0.1)',
          'rgba(0,206,209, 0.2)',
          'rgba(0,206,209, 0.3)',
          'rgba(0,206,209, 0.4)',
          'rgba(0,206,209, 0.5)',
          'rgba(0,206,209, 0.6)',
        ].reverse(),
      },
    },

  },
  series: [
    {
      name: '店铺年销售额',
      type: 'radar',
      areaStyle: {
        color: 'rgba(127,255,210, 0.5)',
      },
      symbol: 'circle',
      symbolSize: 12,
      itemStyle: {
        color: 'rgba(127,255,210,0.8)',
        borderColor: 'rgba(127,255,210,0.2)',
        borderWidth: 10,
      },
      lineStyle: {
        color: 'rgba(127,255,210, 0.6)',
        width: 2,
      },
      label: {
        show: true,
        color: 'rgba(127,255,210,0.8)',
      },
      data: [
        [] as number[],
      ],
    },
  ],
}))

onMounted(async () => {
  await sleep(10)

  updateOptions((opts) => {
    let max: number = 0
    props.data.map((item: any) => {
      max = Math.max(max, item.num)
      return item
    })
    opts.radar.indicator = props.data.map((item: { name: string }) => {
      return {
        name: item.name,
        max,
      }
    })
    opts.series[0].data = [
      props.data.map((item: { num: number }) => {
        return item.num
      }),
    ]
    return opts
  })
})
</script>

<template>
  <div ref="domRef" class="h-300rem" />
</template>

<style lang="scss" scoped>

</style>
