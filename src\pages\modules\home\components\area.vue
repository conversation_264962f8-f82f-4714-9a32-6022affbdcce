<script lang="ts" setup>
import { useEcharts } from '@/hooks/echarts'
import { sleep } from '@/utils'
import { graphic } from 'echarts'

const props = defineProps<{
  data: any
}>()

const { domRef, autoPlay, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(0,0,0,.6)',
    borderColor: 'rgba(12, 143, 103, 1)',
    padding: 8,
    textStyle: {
      color: 'rgba(255,255,255,.9)',
      fontSize: 12,
    },
    axisPointer: {
      lineStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(12, 143, 103, 0)', // 0% 处的颜色
          }, {
            offset: 0.5,
            color: 'rgba(12, 143, 103, 1)', // 100% 处的颜色
          }, {
            offset: 1,
            color: 'rgba(12, 143, 103, 0)', // 100% 处的颜色
          }],
          global: false, // 缺省为 false
        },
      },
    },
    formatter: (params: any) => {
      let html = `<div>${params[0].name}</div>`
      html += `<div class="flex items-center justify-between m-t-5rem"><span>${params[0].marker}${params[0].seriesName}：</span><span class="font-bold font-ddin m-l-10rem">${params[0].value}亩</span></div>`

      const total = params[1].value >= 10000 ? `${(params[1].value / 10000).toFixed(0)}万` : params[1].value
      html += `<div class="flex items-center justify-between m-t-5rem"><span>${params[1].marker}${params[1].seriesName}：</span><span class="font-bold font-ddin m-l-10rem">${total}元</span></div>`

      const per = params[2].value >= 10000 ? `${(params[2].value / 10000).toFixed(0)}万` : params[2].value
      html += `<div class="flex items-center justify-between m-t-5rem"><span>${params[2].marker}${params[2].seriesName}：</span><span class="font-bold font-ddin m-l-10rem">${per}元</span></div>`

      return html
    },
  },
  legend: {
    align: 'left',
    center: 0,
    top: 0,
    type: 'plain',
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
    // icon:'rect',
    itemGap: 20,
    itemWidth: 12,
    icon: 'path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z',
  },
  grid: {
    top: '18%',
    left: '16%',
    right: '8%',
    bottom: '10%',
    // containLabel: true
  },
  xAxis: [{
    type: 'category',
    boundaryGap: false,
    axisLine: { // 坐标轴轴线相关设置。数学上的x轴
      show: true,
      lineStyle: {
        color: 'rgba(90, 246, 253, 0.08)',
      },
    },
    axisLabel: { // 坐标轴刻度标签的相关设置
      color: 'rgba(255,255,255,.7)',
      padding: 4,
      fontSize: 10,
      formatter(data) {
        return data
      },
    },
    splitLine: {
      show: false,
    },
    axisTick: {
      show: true,
    },
    data: ['', '', ''],
  }],
  yAxis: [{
    name: '',
    nameTextStyle: {
      color: 'rgba(255,255,255,.8)',
      fontSize: 12,
      padding: 0,
      align: 'right',
    },
    type: 'log', // 设置为对数轴
    logBase: 2, // 设置对数的底数，可以是10（默认）或2等
    splitLine: {
      show: false,
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: 'rgba(90, 246, 253, 0.08)',
      },
    },
    axisLabel: {
      show: true,
      color: 'rgba(255,255,255,.5)',
      padding: 4,
      fontSize: 10,
      formatter: (value: number) => {
        if (value >= 10000) {
          return `${(value / 10000).toFixed(2)}万`
        }
        return value
      },
    },
    axisTick: {
      show: false,
    },
  }],
  series: [
    {
      name: '种植面积',
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 5,
      smooth: false,
      lineStyle: {
        width: 1,
        color: 'rgba(37,220,167,1)', // 线条颜色
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        borderWidth: 1,
        color: 'rgba(37,220,167,1)',
      },
      tooltip: {
        show: true,
      },
      areaStyle: { // 区域填充样式
        // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
        color: new graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(37,220,167,0.5)',
        }, {
          offset: 1,
          color: 'rgba(37,220,167, 0)',
        }], false),
        shadowColor: 'rgba(37,220,167, 0.5)', // 阴影颜色
        shadowBlur: 20, // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
      },
      data: [0, 0, 0],
    },
    {
      name: '年产值',
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 5,
      smooth: false,
      lineStyle: {
        width: 1,
        color: 'rgba(37,212,220,1)', // 线条颜色
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        borderWidth: 1,
        color: 'rgba(37,212,220,1)',
      },
      tooltip: {
        show: true,
      },
      areaStyle: { // 区域填充样式
        // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
        color: new graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(37,212,220,0.5)',
        }, {
          offset: 1,
          color: 'rgba(37,212,220, 0)',
        }], false),
        shadowColor: 'rgba(37,212,220, 0.5)', // 阴影颜色
        shadowBlur: 20, // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
      },
      data: [0, 0, 0],
    },
    {
      name: '亩产值',
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 5,
      smooth: false,
      lineStyle: {
        width: 1,
        color: 'rgba(233,190,18,1)', // 线条颜色
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        borderWidth: 1,
        color: 'rgba(233,190,18,1)',
      },
      tooltip: {
        show: true,
      },
      areaStyle: { // 区域填充样式
        // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
        color: new graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(233,190,18,0.5)',
        }, {
          offset: 1,
          color: 'rgba(233,190,18, 0)',
        }], false),
        shadowColor: 'rgba(233,190,18, 0.5)', // 阴影颜色
        shadowBlur: 20, // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
      },
      data: [0, 0, 0],
    },
  ],
}))

onMounted(async () => {
  await sleep(10)

  updateOptions((opts) => {
    opts.xAxis[0].data = props.data.map((item: { name: string }) => {
      return item.name
    })
    opts.series.map((item: any, index: number) => {
      item.data = [
        ...props.data.map((item: { num: number[] }) => {
          return item.num[index]
        }),
      ]
      return item
    })
    autoPlay(5, 5000)
    return opts
  })
})
</script>

<template>
  <div ref="domRef" class="h-275rem" />
</template>

<style lang="scss" scoped>
</style>
