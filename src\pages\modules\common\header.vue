<script setup lang="ts">
import { useWeather } from '@/hooks/weather'
import dayjs from 'dayjs'

// 当前菜单
const current = defineModel('current', {
  type: Number,
  default: 0,
})

// 当前时间戳
const timestamp = ref(dayjs())
// 今天天气
const today = ref()
const weather = useWeather()
async function getWeather() {
  today.value = await weather.getToday()
}

const menuList = [
  {
    // name: '产业概览',
    name: '全产业链数字化管理',
    icon: '<EMAIL>',
  },
  {
    // name: '农业物联',
    name: '蚕桑生产过程数字化管理',
    icon: '<EMAIL>',
  },
  {
    // name: '工业物联',
    name: '蚕桑加工过程数字化管理',
    icon: '<EMAIL>',
  },
  {
    // name: '监控云台',
    name: '蚕桑生产加工智能监控管理',
    icon: '<EMAIL>',
  },
]

onMounted(() => {
  getWeather()
  setInterval(() => {
    timestamp.value = dayjs()
  }, 1000)
  setInterval(() => getWeather(), 3600 * 6 * 1000)
})

function getImages(name: string) {
  // 确保你的图片都是这些格式
  const path = `/src/assets/images/${name}`
  const modules = import.meta.glob('/src/assets/images/*.(png|jpg|jpeg|gif|svg)', { eager: true })
  return (modules[path] as any)?.default
}
</script>

<template>
  <div class="header flex items-center justify-between p-x-20rem">
    <div class="logo w-25%">
      <a href="/manager" target="_blank">
        <img class="h-60rem" src="@/assets/images/<EMAIL>" alt="logo">
      </a>
    </div>
    <div class="menu" :class="current === 2 ? 'factory' : ''">
      <NTabs v-if="current !== null" v-model:value="current" animated>
        <NTab v-for="(item, index) in menuList" :key="index" :name="index">
          <div class="flex items-center justify-center">
            <img :src="getImages(item.icon)">
            <div class="m-l-5rem w-90px whitespace-normal text-14px lh-18px">
              {{ item.name }}
            </div>
          </div>
        </NTab>
      </NTabs>
    </div>
    <div class="day w-25% flex items-center justify-end text-white">
      <div class="time flex items-center text-40rem">
        <span>{{ timestamp.format('HH') }}</span>
        <span class="m-x-2rem text-32rem">:</span>
        <span>{{ timestamp.format('mm') }}</span>
        <span class="m-x-2rem text-32rem">:</span>
        <span>{{ timestamp.format('ss') }}</span>
      </div>
      <div class="blank" />
      <div class="date">
        <div>星期{{ ['日', '一', '二', '三', '四', '五', '六'][timestamp.day()] }}</div>
        <div>{{ timestamp.format('YYYY-MM-DD') }}</div>
      </div>
      <div class="blank" />
      <div v-if="today" class="weather flex items-center p-r-30rem">
        <img class="h-40rem w-40rem" :src="weather.getIcon(today.weather)" :alt="today.weather">
        <span class="m-l-5rem">{{ today.weather }}</span>
        <span class="m-l-5rem">{{ today.temperature }}℃</span>
      </div>
    </div>
  </div>
</template>

<style class="scss" scoped>
.header {
  position: absolute;
  z-index: 99;
  left: 0;
  top: 0;
  width: 100%;
  height: 100rem;
  user-select: none;
  .logo {
    cursor: pointer;
  }
  .menu {
    :deep(.n-tabs-nav) {
      .n-tabs-tab {
        position: relative;
        font-family: 'Source Han Sans', 'Microsoft YaHei';
        font-size: 20rem;
        line-height: 20rem;
        width: 216rem;
        height: 56rem;
        justify-content: center;
        text-shadow: 0 2rem 5rem rgba(0, 0, 0, 0.2);
        color: #fff;
        padding: 20rem 0;
        img {
          opacity: 0;
          width: 0;
          height: 0;
          transition: all 0.3s ease;
        }
        &:hover,
        &.n-tabs-tab--active {
          img {
            opacity: 1;
            width: 28rem;
            height: 28rem;
          }
        }
      }
      .n-tabs-bar {
        width: 216rem;
        height: 56rem;
        background: url(@/assets/images/<EMAIL>) no-repeat center top;
        background-size: contain;
      }
      .n-tabs-tab-pad {
        display: none;
      }
    }
    &.factory {
      :deep(.n-tabs-nav) {
        .n-tabs-bar {
          background: url(@/assets/images/<EMAIL>) no-repeat center top;
          background-size: contain;
        }
      }
    }
  }
  .day {
    font-family: DINAlternate, DINAlternate;
    color: #fff;
    .time {
      font-weight: bold;
      font-size: 40rem;
      line-height: 40rem;
    }
    .date {
      font-size: 14rem;
      line-height: 18rem;
    }
    .weather {
      height: 42rem;
      background: url(@/assets/images/<EMAIL>) no-repeat right bottom;
      background-size: 96% 22rem;
      margin-top: -14rem;
      cursor: pointer;
    }
    .blank {
      border-left: 1rem solid #fff;
      height: 24rem;
      margin: 0 20rem;
    }
  }
}
</style>
