<script lang="ts" setup>
import icon from '@/assets/images/<EMAIL>'
import { useEcharts } from '@/hooks/echarts'
import { sleep } from '@/utils'

const props = defineProps<{
  data: any
}>()

function getSeries(data: { num: number, name: string }[]) {
  const series = [] as any
  let sumValue = 0
  data.map((item) => {
    sumValue += item.num
    return item
  })
  const barColor = [
    {
      color1: 'rgba(37, 220, 167, 0.90)',
      color2: '',
    },
    {
      color1: 'rgba(255, 171, 43, 0.90)',
      color2: '',
    },
    {
      color1: 'rgba(37, 212, 220, 0.90)',
      color2: '',
    },
    {
      color1: 'rgba(255, 255, 255, 0.90)',
      color2: '',
    },
  ]
  data.map((item, i) => {
    // 数据环
    series.push({
      type: 'pie',
      clockwise: false, // 顺时加载
      emphasis: {
        scale: false, // 鼠标移入变大
      },
      radius: [`${100 - i * 16}%`, `${100 - (2 * i + 1) * 8}%`],
      center: ['50%', '50%'],
      label: {
        show: false,
      },
      itemStyle: {
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        borderWidth: 5,
      },
      data: [{
        value: item.num,
        name: item.name,
        itemStyle: {
          color: (barColor[i] && barColor[i].color1) || 'rgba(68,165,255,1)',
        },
      }, {
        value: sumValue - item.num,
        name: '',
        itemStyle: {
          color: 'transparent',
        },
        tooltip: {
          show: false,
        },
        emphasis: {
          scale: false, // 鼠标移入变大
        },
      }],
    })
    // 背景环
    series.push({
      name: 'blank',
      type: 'pie',
      silent: true,
      z: 0,
      clockwise: true, // 顺时加载
      emphasis: {
        scale: false, // 鼠标移入变大
      },
      radius: [`${100 - i * 16}%`, `${100 - (2 * i + 1) * 8}%`],
      center: ['50%', '50%'],
      label: {
        show: false,
      },
      itemStyle: {
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        borderWidth: 5,
      },
      data: [{
        value: 1,
        itemStyle: {
          color: 'rgba(255, 255, 255,.13)',
          borderWidth: 0,
        },
        tooltip: {
          show: false,
        },
        emphasis: {
          scale: false, // 鼠标移入变大
        },
      }],
    })
    return item
  })
  return series
}

const { domRef, autoPlay, updateOptions } = useEcharts(() => ({
  tooltip: {
    backgroundColor: 'rgba(0,0,0,.6)',
    borderColor: 'rgba(12, 143, 103, 1)',
    padding: 8,
    textStyle: {
      color: 'rgba(255,255,255,.9)',
      fontSize: 12,
    },
    formatter: (params: any) => {
      let html = ''
      html += `<div class="flex items-center justify-between"><span>${params.marker}${params.name}：</span><span class="font-bold font-ddin m-l-5rem">${params.value}亩</span></div>`
      return html
    },
  },
  graphic: {
    elements: [{
      type: 'image',
      style: {
        image: icon,
        width: 44,
        height: 44,
      },
      left: 'center',
      top: 'center',
    }],
  },
  series: [],
}))

onMounted(async () => {
  await sleep(10)

  const data = [
    {
      name: '桑田',
      num: props.data[0],
    },
    {
      name: '一厂',
      num: props.data[1],
    },
    {
      name: '二厂',
      num: props.data[2],
    },
    {
      name: '三厂',
      num: props.data[3],
    },
  ]

  updateOptions((opts) => {
    opts.series = getSeries(data)
    autoPlay(4, 5000, true)
    return opts
  })
})
</script>

<template>
  <div ref="domRef" class="h-200rem" />
</template>

<style lang="scss" scoped>
</style>
