<script setup lang="ts">
import { type FormRule, useFormRules, useNaiveForm } from '@/hooks/form'
import { loginApi, ynyLoginApi } from '@/service'
import { useAuthStore } from '@/store'
import { autofit } from '@/utils'
import { useRouter } from 'vue-router'

const { formRef, validate } = useNaiveForm()

const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()

onMounted(async () => {
  autofit()
  window.addEventListener('resize', autofit)
})

interface FormModel {
  username: string
  password: string
}

const model: FormModel = reactive({
  username: '',
  password: '',
})

const rules = computed<Record<keyof FormModel, FormRule[]>>(() => {
  const { formRules } = useFormRules()

  return {
    username: formRules.userName,
    password: formRules.pwd,
  }
})

async function handleSubmit() {
  await validate()
  const { data, error } = await loginApi(model.username, model.password)
  if (!error) {
    window.$message?.success('登录成功')
    authStore.setToken(data)
    router.replace('/')
  }
  else {
    window.$message?.error(error.data.msg)
  }
}

async function handleYnyLogin(query: any) {
  const { data, error } = await ynyLoginApi(query)
  if (!error || !data) {
    window.$message?.success('授权成功')
    authStore.setToken(data)
    router.replace('/')
  }
  else {
    window.$message?.error(error.data.msg)
    router.replace('/login')
  }
}

onMounted(() => {
  if (authStore.getToken()) {
    router.replace('/')
  }
  else {
    if (route.query.timestamp && route.query.sign) {
      handleYnyLogin(route.query)
    }
  }
})
</script>

<template>
  <div class="page flex items-center justify-center overflow-hidden">
    <div class="loginForm w-400rem b-rd-15px p-40rem">
      <div class="flex justify-center p-y-5px">
        <img class="h-48rem" src="@/assets/images/<EMAIL>" alt="logo">
      </div>
      <NForm
        v-if="!route.query?.timestamp || !route.query?.sign"
        ref="formRef"
        class="m-t-30px"
        :model="model"
        :rules="rules"
        size="large"
        :show-label="false"
        @keyup.enter="handleSubmit"
      >
        <NFormItem path="userName">
          <NInput v-model:value="model.username" placeholder="请输入用户名" round />
        </NFormItem>
        <NFormItem path="password">
          <NInput v-model:value="model.password" type="password" show-password-on="click" placeholder="请输入密码" round />
        </NFormItem>
        <NSpace class="m-y-10px" vertical>
          <NButton type="primary" size="large" round block @click="handleSubmit">
            立即登录
          </NButton>
        </NSpace>
      </NForm>
      <div v-else class="m-y-20px flex items-center justify-center">
        <NSpin :show="true" size="small" />
        <span class="m-l-10px text-white opacity-50">鉴权中，请稍后...</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/styles/transition.scss';

.page {
  width: 100vw;
  height: 100vh;
  background: #0b2e24;
}
.loginForm {
  border: 2rem solid rgba(37, 220, 167, 0.2);
  background: linear-gradient(180deg, rgba(37, 220, 167, 0.1) 0%, rgba(0, 47, 32, 0.6) 100%);
  box-shadow: 0 0 200rem rgba(89, 238, 193, 0.2);
  :deep(.n-input) {
    --n-border: 1px solid rgba(89, 238, 193, 0.2) !important;
    --n-text-color: #fff !important;
    --n-color: transparent !important;
    --n-placeholder-color: rgba(89, 238, 193, 0.2) !important;
    background-color: transparent !important;
    padding: 0 5rem;
    .n-input__placeholder {
      font-size: 14px;
    }
  }
  :deep(.n-button) {
    background-color: var(--n-color) !important;
  }
}
</style>
