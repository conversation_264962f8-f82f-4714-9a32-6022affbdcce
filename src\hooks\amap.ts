import AMapLoader from '@amap/amap-jsapi-loader'

export async function useAmap(container: HTMLElement, config: {
  [key: string]: string | boolean | number | number[] | any[]
}) {
  // 调整饱和度、亮度、对比度
  container.style.filter = 'saturate(1.2) brightness(1.1) contrast(1.1)'

  // 高德地图密匙
  window._AMapSecurityConfig = {
    securityJsCode: import.meta.env.VITE_AMAP_SECRET,
  }

  // 创建AMap实例
  const AMap = await AMapLoader.load({
    key: import.meta.env.VITE_AMAP_KEY,
    version: '2.0',
  })

  // 创建地图实例
  const map = new AMap.Map(container, {
    zIndex: 2,
    viewMode: '3D',
    pitch: 40,
    zoom: 15,
    zooms: [2, 25],
    center: [0, 0],
    showLabel: true,
    showBuildingBlock: false,
    mapStyle: 'amap://styles/normal',
    // layers: [new AMap.TileLayer.Satellite()],
    ...config,
  })

  // 打印经纬度
  if (import.meta.env.MODE === 'development') {
    map.on('click', (event: { lnglat: { lng: any, lat: any } }) => {
      const { lng, lat } = event.lnglat
      console.log([lng, lat])
    })
  }

  return { AMap, map }
};
