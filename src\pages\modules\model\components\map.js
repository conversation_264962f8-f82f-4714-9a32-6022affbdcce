import labelIcon from '@/assets/texture/label-icon.png'
import emitter from '@/utils/mitt'

import { geoMercator } from 'd3-geo'
import gsap from 'gsap'
import { BaseMap, ExtrudeMap, GradientShader, Grid, Label3d, Line, Mini3d, Particles, Plane } from 'mini3d'
import {
  AdditiveBlending,
  AmbientLight,
  BoxGeometry,
  Color,
  DirectionalLight,
  DoubleSide,
  Fog,
  Group,
  LineBasicMaterial,
  Mesh,
  MeshBasicMaterial,
  MeshLambertMaterial,
  MeshStandardMaterial,
  NearestFilter,
  PlaneGeometry,
  PointLight,
  PointsMaterial,
  QuadraticBezierCurve3,
  RepeatWrapping,
  Sprite,
  SpriteMaterial,
  SRGBColorSpace,
  TubeGeometry,
  Vector3,
} from 'three'
import { InteractionManager } from 'three.interactive'
import cityData from '../data/cityData'
import flyData from '../data/flyData'
import infoData from '../data/infoData'
import provincesData from '../data/provincesData'
import scatterData from '../data/scatter'

// 模型数据
emitter.on('modelData', (data) => {
  provincesData.map((item, index) => {
    item.value = data.output[index]
    return item
  })
  infoData.map((item, index) => {
    item.level = data.parkinfo[index].leader
    item.value = data.parkinfo[index].staff
    return item
  })
  scatterData.map((item) => {
    item.hidden = !data.scatter.includes(item.name)
    return item
  })
  flyData.map((item) => {
    item.hidden = !data.fly.includes(item.name)
    return item
  })
})

function sortByValue(data) {
  data.sort((a, b) => b.value - a.value)
  return data
}
export class World extends Mini3d {
  constructor(canvas, assets) {
    super(canvas)
    // 中心坐标
    this.geoProjectionCenter = [120.515131, 32.686225]
    // 缩放比例
    this.geoProjectionScale = 3600
    // 飞线中心
    this.flyLineCenter = [120.487820, 32.671936]
    // 地图拉伸高度
    this.depth = 0.5
    this.mapFocusLabelInfo = {
      name: '富安镇',
      enName: 'Fu\'an Town',
      center: [120.51207016, 32.61316228],
    }
    // 是否点击
    this.clicked = false
    // 雾
    this.scene.fog = new Fog(0x10372C, 1, 50)
    // 背景
    this.scene.background = new Color(0x10372C)

    // 相机初始位置
    this.camera.instance.position.set(-13.767695123014105, 12.990152163077308, 39.28228164159694)
    this.camera.instance.near = 1
    this.camera.instance.far = 10000
    this.camera.instance.updateProjectionMatrix()
    // 创建交互管理
    this.interactionManager = new InteractionManager(this.renderer.instance, this.camera.instance, this.canvas)
    // 播放状态
    this.playing = false
    this.assets = assets
    // 创建环境光
    this.initEnvironment()
    this.init()
  }

  init() {
    // 标签组
    this.labelGroup = new Group()
    this.label3d = new Label3d(this)
    this.labelGroup.rotation.x = -Math.PI / 2
    this.scene.add(this.labelGroup)
    // 飞线焦点光圈组
    this.flyLineFocusGroup = new Group()
    this.flyLineFocusGroup.visible = false
    this.flyLineFocusGroup.rotation.x = -Math.PI / 2
    this.scene.add(this.flyLineFocusGroup)
    // 区域事件元素
    this.eventElement = []
    // 鼠标移上移除的材质
    this.defaultMaterial = null // 默认材质
    this.defaultLightMaterial = null // 高亮材质
    // 创建底部高亮
    this.createBottomBg()

    // 扩散网格
    this.createGrid()
    // 旋转圆环
    this.createRotateBorder()
    // 创建标签
    this.createLabel()
    // 创建地图
    this.createMap()
    // 创建模糊边界
    this.createChinaBlurLine()
    // 创建视频动画
    // this.createAnimateVideo()
    this.createEvent()
    // 创建飞线
    this.createFlyLine()
    this.createFlyLineFocus()
    // 创建粒子
    this.createParticles()
    // 创建散点图
    this.createScatter()
    // 创建信息点
    this.createInfoPoint()
    // 创建动画时间线
    const tl = gsap.timeline()
    tl.pause()
    this.animateTl = tl
    tl.addLabel('focusMap', 1.5)
    tl.addLabel('focusMapOpacity', 2)
    tl.addLabel('bar', 3)
    tl.to(this.camera.instance.position, {
      duration: 2,
      x: -0.2515849818960619,
      y: 12.397744557047988,
      z: 14.647659671139275,
      ease: 'circ.out',
      onStart: () => {
        this.flyLineFocusGroup.visible = false
      },
    })
    tl.to(
      this.focusMapGroup.position,
      {
        duration: 1,
        x: 0,
        y: 0,
        z: 0,
      },
      'focusMap',
    )

    tl.to(
      this.focusMapGroup.scale,
      {
        duration: 1,
        x: 1,
        y: 1,
        z: 1,
        ease: 'circ.out',
      },
      'focusMap',
    )

    tl.to(
      this.focusMapTopMaterial,
      {
        duration: 1,
        opacity: 1,
        ease: 'circ.out',
      },
      'focusMapOpacity',
    )
    tl.to(
      this.focusMapSideMaterial,
      {
        duration: 1,
        opacity: 1,
        ease: 'circ.out',
        onComplete: () => {
          this.focusMapSideMaterial.transparent = false
        },
      },
      'focusMapOpacity',
    )
    this.otherLabel.map((item, index) => {
      const element = item.element.querySelector('.other-label')
      tl.to(
        element,
        {
          duration: 1,
          delay: 0.1 * index,
          translateY: 0,
          opacity: 1,
          ease: 'circ.out',
        },
        'focusMapOpacity',
      )
    })
    tl.to(
      this.mapLineMaterial,
      {
        duration: 0.5,
        delay: 0.3,
        opacity: 1,
      },
      'focusMapOpacity',
    )
    tl.to(
      this.rotateBorder1.scale,
      {
        delay: 0.3,
        duration: 1,
        x: 1,
        y: 1,
        z: 1,
        ease: 'circ.out',
      },
      'focusMapOpacity',
    )
    tl.to(
      this.rotateBorder2.scale,
      {
        duration: 1,
        delay: 0.5,
        x: 1,
        y: 1,
        z: 1,
        ease: 'circ.out',
        onComplete: () => {
          this.flyLineFocusGroup.visible = true
        },
      },
      'focusMapOpacity',
    )
    this.allBar.map((item, index) => {
      tl.to(
        item.scale,
        {
          duration: 1,
          delay: 0.1 * index,
          x: 1,
          y: 1,
          z: 1,
          ease: 'circ.out',
        },
        'bar',
      )
    })
    this.allBarMaterial.map((item, index) => {
      tl.to(
        item,
        {
          duration: 1,
          delay: 0.1 * index,
          opacity: 1,
          ease: 'circ.out',
        },
        'bar',
      )
    })

    this.allProvinceLabel.map((item, index) => {
      const element = item.element.querySelector('.provinces-label-wrap')
      const number = item.element.querySelector('.number .value')
      const numberVal = Number(number.textContent)
      const numberAnimate = {
        score: 0,
      }
      tl.to(
        element,
        {
          duration: 1,
          delay: 0.2 * index,
          translateY: 0,
          opacity: 1,
          ease: 'circ.out',
        },
        'bar',
      )
      tl.to(
        numberAnimate,
        {
          duration: 1,
          delay: 0.2 * index,
          score: numberVal,
          onUpdate: showScore,
        },
        'bar',
      )
      function showScore() {
        number.textContent = numberAnimate.score.toFixed(0)
      }
    })
    this.allGuangquan.map((item, index) => {
      tl.to(
        item.children[0].scale,
        {
          duration: 1,
          delay: 0.1 * index,
          x: 1,
          y: 1,
          z: 1,
          ease: 'circ.out',
        },
        'bar',
      )
      tl.to(
        item.children[1].scale,
        {
          duration: 1,
          delay: 0.1 * index,
          x: 1,
          y: 1,
          z: 1,
          ease: 'circ.out',
        },
        'bar',
      )
    })
  }

  initEnvironment() {
    const sun = new AmbientLight(0xFFFFFF, 3)
    this.scene.add(sun)
    const directionalLight = new DirectionalLight(0xFFFFFF, 8)
    directionalLight.position.set(-30, 6, -8)
    directionalLight.castShadow = true
    directionalLight.shadow.radius = 20
    directionalLight.shadow.mapSize.width = 1024
    directionalLight.shadow.mapSize.height = 1024
    this.scene.add(directionalLight)
    this.createPointLight({
      color: '#1D6366',
      intensity: 600,
      distance: 10000,
      x: -9,
      y: 3,
      z: -3,
    })
    this.createPointLight({
      color: '#1D6366',
      intensity: 230,
      distance: 10000,
      x: 0,
      y: 3,
      z: 6,
    })
  }

  createPointLight(pointParams) {
    const pointLight = new PointLight(0x1D6366, pointParams.intensity, pointParams.distance)
    pointLight.position.set(pointParams.x, pointParams.y, pointParams.z)
    this.scene.add(pointLight)
  }

  createMap() {
    const mapGroup = new Group()
    const focusMapGroup = new Group()
    this.focusMapGroup = focusMapGroup
    const { china, chinaTopLine } = this.createChina()
    const { map, mapTop, mapLine } = this.createProvince()
    china.setParent(mapGroup)
    chinaTopLine.setParent(mapGroup)

    map.setParent(focusMapGroup)
    mapTop.setParent(focusMapGroup)
    mapLine.setParent(focusMapGroup)
    focusMapGroup.position.set(0, 0, -0.01)
    focusMapGroup.scale.set(1, 1, 0)
    mapGroup.add(focusMapGroup)
    mapGroup.rotateX(-Math.PI / 2)
    mapGroup.position.set(0, 0.2, 0)
    this.scene.add(mapGroup)
    this.createBar()
  }

  createChina() {
    const params = {
      chinaBgMaterialColor: '#164A3B',
      lineColor: '#3ECDA3',
    }
    const chinaData = this.assets.instance.getResource('china')
    const chinaBgMaterial = new MeshLambertMaterial({
      color: new Color(params.chinaBgMaterialColor),
      transparent: true,
      opacity: 1,
    })
    const china = new BaseMap(this, {
      data: chinaData,
      geoProjectionCenter: this.geoProjectionCenter,
      geoProjectionScale: this.geoProjectionScale,
      merge: false,
      material: chinaBgMaterial,
      renderOrder: 2,
    })
    const chinaTopLineMaterial = new LineBasicMaterial({
      color: params.lineColor,
    })
    const chinaTopLine = new Line(this, {
      data: chinaData,
      geoProjectionCenter: this.geoProjectionCenter,
      geoProjectionScale: this.geoProjectionScale,
      material: chinaTopLineMaterial,
      renderOrder: 3,
    })
    chinaTopLine.lineGroup.position.z += 0.01
    return { china, chinaTopLine }
  }

  createProvince() {
    const mapJsonData = this.assets.instance.getResource('mapJson')
    const [topMaterial, sideMaterial] = this.createProvinceMaterial()
    this.focusMapTopMaterial = topMaterial
    this.focusMapSideMaterial = sideMaterial
    const map = new ExtrudeMap(this, {
      geoProjectionCenter: this.geoProjectionCenter,
      geoProjectionScale: this.geoProjectionScale,
      position: new Vector3(0, 0, 0.11),
      data: mapJsonData,
      depth: this.depth,
      topFaceMaterial: topMaterial,
      sideMaterial,
      renderOrder: 9,
    })
    const faceMaterial = new MeshStandardMaterial({
      color: 0xFFFFFF,
      transparent: true,
      opacity: 0.5,
    })
    const faceGradientShader = new GradientShader(faceMaterial, {
      uColor1: 0x2A9374,
      uColor2: 0x10372C,
    })
    this.defaultMaterial = faceMaterial
    this.defaultLightMaterial = this.defaultMaterial.clone()
    this.defaultLightMaterial.emissive.setHex(0x0B2E24)
    this.defaultLightMaterial.emissiveIntensity = 3.5
    const mapTop = new BaseMap(this, {
      geoProjectionCenter: this.geoProjectionCenter,
      geoProjectionScale: this.geoProjectionScale,
      position: new Vector3(0, 0, this.depth + 0.22),
      data: mapJsonData,
      material: faceMaterial,
      renderOrder: 2,
    })
    mapTop.mapGroup.children.map((group) => {
      group.children.map((mesh) => {
        if (mesh.type === 'Mesh') {
          this.eventElement.push(mesh)
        }
      })
    })
    this.mapLineMaterial = new LineBasicMaterial({
      color: 0xFFFFFF,
      opacity: 0,
      transparent: true,
      fog: false,
    })
    const mapLine = new Line(this, {
      geoProjectionCenter: this.geoProjectionCenter,
      geoProjectionScale: this.geoProjectionScale,
      data: mapJsonData,
      material: this.mapLineMaterial,
      renderOrder: 3,
    })
    mapLine.lineGroup.position.z += this.depth + 0.23
    return {
      map,
      mapTop,
      mapLine,
    }
  }

  createProvinceMaterial() {
    const topMaterial = new MeshLambertMaterial({
      color: 0xFFFFFF,
      transparent: true,
      opacity: 0,
      fog: false,
      side: DoubleSide,
    })
    topMaterial.onBeforeCompile = (shader) => {
      shader.uniforms = {
        ...shader.uniforms,
        uColor1: { value: new Color(0x2A9374) },
        uColor2: { value: new Color(0x10372C) },
      }
      shader.vertexShader = shader.vertexShader.replace(
        'void main() {',
        `
        attribute float alpha;
        varying vec3 vPosition;
        varying float vAlpha;
        void main() {
          vAlpha = alpha;
          vPosition = position;
      `,
      )
      shader.fragmentShader = shader.fragmentShader.replace(
        'void main() {',
        `
        varying vec3 vPosition;
        varying float vAlpha;
        uniform vec3 uColor1;
        uniform vec3 uColor2;
        void main() {
      `,
      )
      shader.fragmentShader = shader.fragmentShader.replace(
        '#include <opaque_fragment>',
        /* glsl */ `
      #ifdef OPAQUE
      diffuseColor.a = 1.0;
      #endif
            #ifdef USE_TRANSMISSION
      diffuseColor.a *= transmissionAlpha + 0.1;
      #endif
      vec3 gradient = mix(uColor1, uColor2, vPosition.x/15.78);       outgoingLight = outgoingLight*gradient;
      float topAlpha = 0.5;
      if(vPosition.z>0.3){
        diffuseColor.a *= topAlpha;
      }
      gl_FragColor = vec4( outgoingLight, diffuseColor.a  );
      `,
      )
    }
    const sideMap = this.assets.instance.getResource('side')
    sideMap.wrapS = RepeatWrapping
    sideMap.wrapT = RepeatWrapping
    sideMap.repeat.set(1, 1.5)
    sideMap.offset.y += 0.065
    const sideMaterial = new MeshStandardMaterial({
      color: 0xFFFFFF,
      map: sideMap,
      fog: false,
      opacity: 0,
      side: DoubleSide,
    })
    this.time.on('tick', () => {
      sideMap.offset.y += 0.005
    })
    sideMaterial.onBeforeCompile = (shader) => {
      shader.uniforms = {
        ...shader.uniforms,
        uColor1: { value: new Color(0x2A9374) },
        uColor2: { value: new Color(0x2A9374) },
      }
      shader.vertexShader = shader.vertexShader.replace(
        'void main() {',
        `
        attribute float alpha;
        varying vec3 vPosition;
        varying float vAlpha;
        void main() {
          vAlpha = alpha;
          vPosition = position;
      `,
      )
      shader.fragmentShader = shader.fragmentShader.replace(
        'void main() {',
        `
        varying vec3 vPosition;
        varying float vAlpha;
        uniform vec3 uColor1;
        uniform vec3 uColor2;
        void main() {
      `,
      )
      shader.fragmentShader = shader.fragmentShader.replace(
        '#include <opaque_fragment>',
        /* glsl */ `
      #ifdef OPAQUE
      diffuseColor.a = 1.0;
      #endif
            #ifdef USE_TRANSMISSION
      diffuseColor.a *= transmissionAlpha + 0.1;
      #endif
      vec3 gradient = mix(uColor1, uColor2, vPosition.z/1.2);
      outgoingLight = outgoingLight*gradient;
      gl_FragColor = vec4( outgoingLight, diffuseColor.a  );
      `,
      )
    }
    return [topMaterial, sideMaterial]
  }

  createBar() {
    const self = this
    const data = sortByValue(provincesData).filter((item, index) => index < 7)
    const barGroup = new Group()
    this.barGroup = barGroup
    const factor = 0.7
    const height = 4.0 * factor
    const max = data[0].value
    this.allBar = []
    this.allBarMaterial = []
    this.allGuangquan = []
    this.allProvinceLabel = []
    data.map((item, index) => {
      const geoHeight = height * (item.value / max)
      const material = new MeshBasicMaterial({
        color: 0xFFFFFF,
        transparent: true,
        opacity: 0,
        depthTest: false,
        fog: false,
      })
      new GradientShader(material, {
        uColor1: index > 1 ? 0xFECF88 : 0x4EFDCB,
        uColor2: index > 1 ? 0xFFF9F0 : 0x76F8FE,
        size: geoHeight,
        dir: 'y',
      })
      const geo = new BoxGeometry(0.1 * factor, 0.1 * factor, geoHeight)
      geo.translate(0, 0, geoHeight / 2)
      const mesh = new Mesh(geo, material)
      mesh.renderOrder = 5
      const areaBar = mesh
      const [x, y] = this.geoProjection(item.centroid)
      areaBar.position.set(x, -y, this.depth + 0.45)
      areaBar.scale.set(1, 1, 0)
      const guangQuan = this.createQuan(new Vector3(x, this.depth + 0.44, y), index)
      const hg = this.createHUIGUANG(geoHeight, index > 1 ? 0xFFF9F0 : 0x76F8FE)
      areaBar.add(...hg)
      barGroup.add(areaBar)
      barGroup.rotation.x = -Math.PI / 2
      const barLabel = labelStyle04(item, index, new Vector3(x, -y, this.depth + 1.1 + geoHeight))
      this.allBar.push(areaBar)
      this.allBarMaterial.push(material)
      this.allGuangquan.push(guangQuan)
      this.allProvinceLabel.push(barLabel)
    })
    this.scene.add(barGroup)
    function labelStyle04(data, index, position) {
      const label = self.label3d.create('', 'provinces-label', false)
      label.init(
        `<div class="provinces-label ${index > 1 ? 'yellow' : ''}">
      <div class="provinces-label-wrap">
        <div class="number"><span class="value">${data.value}</span><span class="unit">万元</span></div>
        <div class="name">
          <span class="zh">${data.name}</span>
          <span class="en">${data.enName.toUpperCase()}</span>
        </div>
        <div class="no">${index + 1}</div>
      </div>
    </div>`,
        position,
      )
      self.label3d.setLabelStyle(label, 0.01, 'x')
      label.setParent(self.labelGroup)
      return label
    }
  }

  createEvent() {
    const self = this
    let objectsHover = []
    const reset = (mesh) => {
      mesh.traverse((obj) => {
        if (obj.isMesh) {
          obj.material = this.defaultMaterial
        }
      })
    }
    const move = (mesh) => {
      mesh.traverse((obj) => {
        if (obj.isMesh) {
          obj.material = this.defaultLightMaterial
        }
      })
    }
    this.eventElement.map((mesh) => {
      // 创建地区名称label
      function labelStyle01(data, label3d, mesh) {
        const label = label3d.create('', `area-label`, true)
        const [x, y] = self.geoProjection([data.lng, data.lat])
        label.init(
          `<div class="other-label">${mesh.userData.name}</div>`,
          new Vector3(x, -y, 0.4),
        )
        label3d.setLabelStyle(label, 0.02, 'x')
        label.visible = false
        label.setParent(mesh)
        return label
      }
      const res = scatterData.find(item => item.name === mesh.userData.name)
      labelStyle01(res, this.label3d, mesh)

      this.interactionManager.add(mesh)
      mesh.addEventListener('mousedown', (ev) => {
        console.log(ev.target.userData.name)
      })
      mesh.addEventListener('mouseover', (event) => {
        if (!objectsHover.includes(event.target.parent)) {
          objectsHover.push(event.target.parent)
        }
        objectsHover[0].children[0].children[0].visible = true
        document.body.style.cursor = 'pointer'
        move(event.target.parent)
      })
      mesh.addEventListener('mouseout', (event) => {
        objectsHover[0].children[0].children[0].visible = false
        objectsHover = objectsHover.filter(n => n.userData.name !== event.target.parent.userData.name)
        if (objectsHover.length > 0) {
          const mesh = objectsHover[objectsHover.length - 1]
        }
        reset(event.target.parent)
        document.body.style.cursor = 'default'
      })
    })
  }

  createHUIGUANG(h, color) {
    const geometry = new PlaneGeometry(0.35, h)
    geometry.translate(0, h / 2, 0)
    const texture = this.assets.instance.getResource('huiguang')
    texture.colorSpace = SRGBColorSpace
    texture.wrapS = RepeatWrapping
    texture.wrapT = RepeatWrapping
    const material = new MeshBasicMaterial({
      color,
      map: texture,
      transparent: true,
      opacity: 0.4,
      depthWrite: false,
      side: DoubleSide,
      blending: AdditiveBlending,
    })
    const mesh = new Mesh(geometry, material)
    mesh.renderOrder = 10
    mesh.rotateX(Math.PI / 2)
    const mesh2 = mesh.clone()
    const mesh3 = mesh.clone()
    mesh2.rotateY((Math.PI / 180) * 60)
    mesh3.rotateY((Math.PI / 180) * 120)
    return [mesh, mesh2, mesh3]
  }

  createQuan(position, index) {
    const guangquan1 = this.assets.instance.getResource('guangquan1')
    const guangquan2 = this.assets.instance.getResource('guangquan2')
    const geometry = new PlaneGeometry(0.5, 0.5)
    const material1 = new MeshBasicMaterial({
      color: 0xFFFFFF,
      map: guangquan1,
      alphaMap: guangquan1,
      opacity: 1,
      transparent: true,
      depthTest: false,
      fog: false,
      blending: AdditiveBlending,
    })
    const material2 = new MeshBasicMaterial({
      color: 0xFFFFFF,
      map: guangquan2,
      alphaMap: guangquan2,
      opacity: 1,
      transparent: true,
      depthTest: false,
      fog: false,
      blending: AdditiveBlending,
    })
    const mesh1 = new Mesh(geometry, material1)
    const mesh2 = new Mesh(geometry, material2)
    mesh1.renderOrder = 6
    mesh2.renderOrder = 6
    mesh1.rotateX(-Math.PI / 2)
    mesh2.rotateX(-Math.PI / 2)
    mesh1.position.copy(position)
    mesh2.position.copy(position)
    mesh2.position.y -= 0.001
    mesh1.scale.set(0, 0, 0)
    mesh2.scale.set(0, 0, 0)
    this.quanGroup = new Group()
    this.quanGroup.add(mesh1, mesh2)
    this.scene.add(this.quanGroup)
    this.time.on('tick', () => {
      mesh1.rotation.z += 0.05
    })
    return this.quanGroup
  }

  createGrid() {
    new Grid(this, {
      gridSize: 50,
      gridDivision: 20,
      gridColor: 0x1B7259,
      shapeSize: 0.5,
      shapeColor: 0x298C6F,
      pointSize: 0.1,
      pointColor: 0x148061,
      diffuse: true,
      diffuseSpeed: 10.0,
      diffuseColor: 0x2CD7A6,
    })
  }

  createBottomBg() {
    const geometry = new PlaneGeometry(20, 20)
    const texture = this.assets.instance.getResource('ocean')
    texture.colorSpace = SRGBColorSpace
    texture.wrapS = RepeatWrapping
    texture.wrapT = RepeatWrapping
    texture.repeat.set(1, 1)
    const material = new MeshBasicMaterial({
      map: texture,
      opacity: 1,
    })
    const mesh = new Mesh(geometry, material)
    mesh.rotation.x = -Math.PI / 2
    mesh.position.set(0, -0.7, 0)
    this.scene.add(mesh)
  }

  /**
   * 创建中国模糊边界
   */
  createChinaBlurLine() {
    const geometry = new PlaneGeometry(147, 147)
    const texture = this.assets.instance.getResource('chinaBlurLine')
    texture.colorSpace = SRGBColorSpace
    texture.wrapS = RepeatWrapping
    texture.wrapT = RepeatWrapping
    // ocean.magFilter = NearestFilter
    texture.generateMipmaps = false
    texture.minFilter = NearestFilter
    texture.repeat.set(1, 1)
    const material = new MeshBasicMaterial({
      color: 0x3ECDA3,
      alphaMap: texture,
      transparent: true,
      opacity: 0.5,
      // blending: AdditiveBlending,
    })
    const mesh = new Mesh(geometry, material)
    mesh.rotateX(-Math.PI / 2)
    mesh.position.set(-32.2, -0.6, 3.2)
    mesh.scale.set(1, 1, 1)
    this.scene.add(mesh)
  }

  /**
   * 创建视频动画
   */
  // createAnimateVideo() {
  //   this.createAnimateVideoItem('.map-video1', new Vector3(11, 0.4, 1))
  //   this.createAnimateVideoItem('.map-video2', new Vector3(-11, 0.4, 2))
  // }

  // createAnimateVideoItem(className, position) {
  //   const video = document.querySelector(className)
  //   window.addEventListener('pointerdown', () => {
  //     video.play()
  //   })
  //   const texture = new VideoTexture(video)
  //   texture.colorSpace = SRGBColorSpace
  //   const scale = 1.2
  //   const geometry = new PlaneGeometry(2.5 * scale, 1 * scale)
  //   const material = new MeshBasicMaterial({
  //     color: 0xA6F6FA,
  //     alphaMap: texture,
  //     transparent: true,
  //     opacity: 1,
  //     blending: AdditiveBlending,
  //   })
  //   const mesh = new Mesh(geometry, material)
  //   mesh.rotateX(-Math.PI / 2)
  //   mesh.position.copy(position)
  //   mesh.renderOrder = 10
  //   this.scene.add(mesh)
  // }

  createLabel() {
    const self = this
    const labelGroup = this.labelGroup
    const label3d = this.label3d
    const otherLabel = []
    cityData.map((province) => {
      if (province.hide === true)
        return false
      const label = labelStyle01(province, label3d, labelGroup)
      otherLabel.push(label)
    })
    const mapFocusLabel = labelStyle02(
      {
        ...this.mapFocusLabelInfo,
      },
      label3d,
      labelGroup,
    )
    const iconLabel1 = labelStyle03(
      {
        icon: labelIcon,
        center: [114.486506, 32.583991],
        width: '20px',
        height: '20px',
        reflect: true,
      },
      label3d,
      labelGroup,
    )
    const iconLabel2 = labelStyle03(
      {
        icon: labelIcon,
        center: [124.486506, 31.983991],
        width: '20px',
        height: '20px',
        reflect: false,
      },
      label3d,
      labelGroup,
    )
    otherLabel.push(mapFocusLabel)
    otherLabel.push(iconLabel1)
    otherLabel.push(iconLabel2)
    this.otherLabel = otherLabel
    function labelStyle01(province, label3d, labelGroup) {
      const label = label3d.create('', `china-label ${province.blur ? ' blur' : ''}`, false)
      const [x, y] = self.geoProjection(province.center)
      label.init(
        `<div class="other-label"><img class="label-icon" src="${labelIcon}">${province.name}</div>`,
        new Vector3(x, -y, 0.4),
      )
      label3d.setLabelStyle(label, 0.02, 'x')
      label.setParent(labelGroup)
      return label
    }
    function labelStyle02(province, label3d, labelGroup) {
      const label = label3d.create('', 'map-label', false)
      const [x, y] = self.geoProjection(province.center)
      label.init(
        `<div class="other-label"><span>${province.name}</span><span>${province.enName}</span></div>`,
        new Vector3(x, -y, 0.4),
      )
      label3d.setLabelStyle(label, 0.015, 'x')
      label.setParent(labelGroup)
      return label
    }
    function labelStyle03(data, label3d, labelGroup) {
      const label = label3d.create('', `decoration-label  ${data.reflect ? ' reflect' : ''}`, false)
      const [x, y] = self.geoProjection(data.center)
      label.init(
        `<div class="other-label"><img class="label-icon" style="width:${data.width};height:${data.height}" src="${data.icon}">`,
        new Vector3(x, -y, 0.4),
      )
      label3d.setLabelStyle(label, 0.02, 'x')
      label.setParent(labelGroup)
      return label
    }
    function labelStyle04(data, label3d, labelGroup, index) {
      const label = label3d.create('', 'provinces-label', false)
      const [x, y] = self.geoProjection(data.center)
      label.init(
        `<div class="provinces-label">
      <div class="provinces-label-wrap">
        <div class="number">${data.value}<span>万人</span></div>
        <div class="name">
          <span class="zh">${data.name}</span>
          <span class="en">${data.enName.toUpperCase()}</span>
        </div>
        <div class="no">${index + 1}</div>
      </div>
    </div>`,
        new Vector3(x, -y, 2.4),
      )
      label3d.setLabelStyle(label, 0.02, 'x')
      label.setParent(labelGroup)
      return label
    }
  }

  createRotateBorder() {
    const max = 12
    const rotationBorder1 = this.assets.instance.getResource('rotationBorder1')
    const rotationBorder2 = this.assets.instance.getResource('rotationBorder2')
    const plane01 = new Plane(this, {
      width: max * 1.178,
      needRotate: true,
      rotateSpeed: 0.001,
      material: new MeshBasicMaterial({
        map: rotationBorder1,
        color: 0x28A5AA,
        transparent: true,
        opacity: 0.2,
        side: DoubleSide,
        depthWrite: false,
        blending: AdditiveBlending,
      }),
      position: new Vector3(0, 0.28, 0),
    })
    plane01.instance.rotation.x = -Math.PI / 2
    plane01.instance.renderOrder = 6
    plane01.instance.scale.set(0, 0, 0)
    plane01.setParent(this.scene)
    const plane02 = new Plane(this, {
      width: max * 1.116,
      needRotate: true,
      rotateSpeed: -0.004,
      material: new MeshBasicMaterial({
        map: rotationBorder2,
        color: 0x28A5AA,
        transparent: true,
        opacity: 0.4,
        side: DoubleSide,
        depthWrite: false,
        blending: AdditiveBlending,
      }),
      position: new Vector3(0, 0.3, 0),
    })
    plane02.instance.rotation.x = -Math.PI / 2
    plane02.instance.renderOrder = 6
    plane02.instance.scale.set(0, 0, 0)
    plane02.setParent(this.scene)
    this.rotateBorder1 = plane01.instance
    this.rotateBorder2 = plane02.instance
  }

  createFlyLine() {
    this.flyLineGroup = new Group()
    this.flyLineGroup.visible = false
    this.scene.add(this.flyLineGroup)
    const texture = this.assets.instance.getResource('mapFlyline')
    texture.wrapS = texture.wrapT = RepeatWrapping
    texture.repeat.set(1, 1)
    const tubeRadius = 0.03
    const tubeSegments = 32
    const tubeRadialSegments = 8
    const closed = false
    const [centerX, centerY] = this.geoProjection(this.flyLineCenter)
    const centerPoint = new Vector3(centerX, -centerY, 0)
    const material = new MeshBasicMaterial({
      map: texture,
      alphaMap: texture,
      color: 0x296F73,
      transparent: true,
      fog: false,
      opacity: 1,
      depthTest: false,
      blending: AdditiveBlending,
    })
    this.time.on('tick', () => {
      texture.offset.x -= 0.006
    })
    flyData
      .filter(item => !item.hidden)
      .map((city) => {
        const [x, y] = this.geoProjection([city.lng, city.lat])
        const point = new Vector3(x, -y, 0)
        const center = new Vector3()
        center.addVectors(centerPoint, point).multiplyScalar(0.5)
        center.setZ(3)
        const curve = new QuadraticBezierCurve3(centerPoint, center, point)
        const tubeGeometry = new TubeGeometry(curve, tubeSegments, tubeRadius, tubeRadialSegments, closed)
        const mesh = new Mesh(tubeGeometry, material)
        mesh.rotation.x = -Math.PI / 2
        mesh.position.set(0, this.depth + 0.44, 0)
        mesh.renderOrder = 21
        this.flyLineGroup.add(mesh)
      })
  }

  createFlyLineFocus() {
    const [x, y] = this.geoProjection(this.flyLineCenter)
    this.flyLineFocusGroup.position.set(x, this.depth + 0.442, y)
    const flyLineFocus = this.assets.instance.getResource('flyLineFocus')
    const geometry = new PlaneGeometry(1, 1)
    const material = new MeshBasicMaterial({
      color: 0xFFFFFF,
      map: flyLineFocus,
      alphaMap: flyLineFocus,
      transparent: true,
      fog: false,
      depthTest: false,
      blending: AdditiveBlending,
    })
    const mesh = new Mesh(geometry, material)
    mesh.scale.set(0, 0, 0)
    const mesh2 = mesh.clone()
    mesh2.material = material.clone()
    this.flyLineFocusGroup.add(mesh, mesh2)
    gsap.to(mesh.material, {
      opacity: 0,
      repeat: -1,
      yoyo: false,
      duration: 1,
    })
    gsap.to(mesh.scale, {
      x: 1.5,
      y: 1.5,
      z: 1.5,
      repeat: -1,
      yoyo: false,
      duration: 1,
    })
    gsap.to(mesh2.material, {
      delay: 0.5,
      opacity: 0,
      repeat: -1,
      yoyo: false,
      duration: 1,
    })
    gsap.to(mesh2.scale, {
      delay: 0.5,
      x: 1.5,
      y: 1.5,
      z: 1.5,
      repeat: -1,
      yoyo: false,
      duration: 1,
    })
  }

  createParticles() {
    this.particles = new Particles(this, {
      num: 10,
      range: 30,
      dir: 'up',
      speed: 0.05,
      material: new PointsMaterial({
        map: Particles.createTexture(),
        size: 1,
        color: 0x00E5F0,
        transparent: true,
        opacity: 1,
        depthTest: false,
        depthWrite: false,
        vertexColors: true,
        blending: AdditiveBlending,
        sizeAttenuation: true,
      }),
    })
    this.particleGroup = new Group()
    this.scene.add(this.particleGroup)
    this.particleGroup.rotation.x = -Math.PI / 2
    this.particles.setParent(this.particleGroup)
    this.particles.enable = false
    this.particleGroup.visible = false
  }

  createScatter() {
    this.scatterGroup = new Group()
    this.scatterGroup.visible = false
    this.scatterGroup.rotation.x = -Math.PI / 2
    this.scene.add(this.scatterGroup)
    const texture = this.assets.instance.getResource('arrow')
    const material = new SpriteMaterial({
      map: texture,
      color: 0xFFF9F0,
      fog: false,
      transparent: true,
      depthTest: false,
    })
    const scatterAllData = sortByValue(scatterData)
    const max = scatterAllData[0].value
    scatterAllData.map((data) => {
      if (data.hidden)
        return
      const sprite = new Sprite(material)
      sprite.renderOrder = 23
      const scale = 0.1 + (data.value / max) * 0.2
      sprite.scale.set(scale, scale, scale)
      const [x, y] = this.geoProjection([data.lng, data.lat])
      sprite.position.set(x, -y, this.depth + 0.45)
      sprite.userData.position = [x, -y, this.depth + 0.45]
      this.scatterGroup.add(sprite)
    })
  }

  createInfoPoint() {
    const self = this
    this.InfoPointGroup = new Group()
    this.scene.add(this.InfoPointGroup)
    this.InfoPointGroup.visible = false
    this.InfoPointGroup.rotation.x = -Math.PI / 2
    this.infoPointIndex = 0
    this.infoPointLabelTime = null
    this.infoLabelElement = []
    const label3d = this.label3d
    const texture = this.assets.instance.getResource('point')
    const colors = [0xFFF9F0, 0x76F8FE]
    const infoAllData = sortByValue(infoData)
    const max = infoAllData[0].value
    infoAllData.map((data, index) => {
      const material = new SpriteMaterial({
        map: texture,
        color: colors[index % colors.length],
        fog: false,
        transparent: true,
        depthTest: false,
      })
      const sprite = new Sprite(material)
      sprite.renderOrder = 23
      const scale = 0.7 + (data.value / max) * 0.4
      sprite.scale.set(scale / 1.5, scale / 1.1, scale)
      const [x, y] = this.geoProjection([data.lng, data.lat])
      const position = [x, -y, this.depth + 0.7]
      sprite.position.set(...position)
      sprite.userData.position = [...position]
      sprite.userData = {
        position: [x, -y, this.depth + 0.7],
        name: data.name,
        value: data.value,
        level: data.level,
        index,
      }
      this.InfoPointGroup.add(sprite)
      const label = infoLabel(data, label3d, this.InfoPointGroup)
      this.infoLabelElement.push(label)
      this.interactionManager.add(sprite)
      sprite.addEventListener('mousedown', (ev) => {
        if (this.clicked || !this.InfoPointGroup.visible)
          return false
        this.clicked = true
        this.infoPointIndex = ev.target.userData.index
        this.infoLabelElement.map((label) => {
          label.visible = false
        })
        label.visible = true
        this.createInfoPointLabelLoop()
      })
      sprite.addEventListener('mouseup', (ev) => {
        this.clicked = false
      })
      sprite.addEventListener('mouseover', (event) => {
        document.body.style.cursor = 'pointer'
      })
      sprite.addEventListener('mouseout', (event) => {
        document.body.style.cursor = 'default'
      })
    })
    function infoLabel(data, label3d, labelGroup) {
      const label = label3d.create('', 'info-point', true)
      const [x, y] = self.geoProjection([data.lng, data.lat])
      label.init(
        ` <div class="info-point-wrap">
          <div class="info-point-wrap-inner">
            <div class="info-point-line">
              <div class="line"></div>
              <div class="line"></div>
              <div class="line"></div>
            </div>
            <div class="info-point-content">
              <div class="content-item"><span class="label">名称</span><span class="value">${data.name}</span></div>
              <div class="content-item"><span class="label">责任人</span><span class="value">${data.level}</span></div>
              <div class="content-item"><span class="label">员工数</span><span class="value">${data.value} 人</span></div>
            </div>
          </div>
        </div>
      `,
        new Vector3(x, -y, self.depth + 1.9),
      )
      label3d.setLabelStyle(label, 0.015, 'x')
      label.setParent(labelGroup)
      label.visible = false
      return label
    }
  }

  createInfoPointLabelLoop() {
    clearInterval(this.infoPointLabelTime)
    this.infoPointLabelTime = setInterval(() => {
      this.infoPointIndex++
      if (this.infoPointIndex >= this.infoLabelElement.length) {
        this.infoPointIndex = 0
      }
      this.infoLabelElement.map((label, i) => {
        if (this.infoPointIndex === i) {
          label.visible = true
        }
        else {
          label.visible = false
        }
      })
    }, 3000)
  }

  geoProjection(args) {
    return geoMercator().center(this.geoProjectionCenter).scale(this.geoProjectionScale).translate([0, 0])(args)
  }

  update() {
    super.update()
    this.interactionManager && this.interactionManager.update()
  }

  destroy() {
    super.destroy()
    this.label3d && this.label3d.destroy()
  }
}
