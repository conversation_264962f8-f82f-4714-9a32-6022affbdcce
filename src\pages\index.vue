<script setup lang="ts">
import { useAuthStore } from '@/store'
import { autofit } from '@/utils'
import emitter from '@/utils/mitt'

import CameraLeft from './modules/camera/left.vue'
import CameraRight from './modules/camera/right.vue'

import Header from './modules/common/header.vue'
import FactoryLeft from './modules/factory/left.vue'
import FactoryRight from './modules/factory/right.vue'

import FarmLeft from './modules/farm/left.vue'
import FarmRight from './modules/farm/right.vue'

import HomeCenter from './modules/home/<USER>'
import HomeExpert from './modules/home/<USER>'
import HomeLeft from './modules/home/<USER>'

import HomeRight from './modules/home/<USER>'
import Model from './modules/model/index.vue'

// 接口
import { deviceListApi, dictApi, expertApi } from '@/service'

const dict = ref()
const expertList = ref()
const deviceList = ref()

function getDict(code: string) {
  let filterDict = {}
  dict.value.forEach((item: any) => {
    if (item.code.includes(code))
      filterDict = { ...filterDict, [item.code.replace(code, '')]: item.value }
  })
  return filterDict
}

function getData() {
  deviceListApi().then((res) => {
    if (res.code === 200) {
      deviceList.value = res.data.data.map((item: any) => {
        item.lastData = Object.assign({ switch: item.switch }, JSON.parse(item.lastData))
        return item
      })
      emitter.emit('deviceData', deviceList.value)
    }
  })
}

onMounted(() => {
  const authStore = useAuthStore()
  const token = authStore.getToken()
  const route = useRoute()
  if (!token || route.query.sign) {
    authStore.clearToken()
    const router = useRouter()
    router.replace({
      path: '/login',
      query: route.query,
    })
  }
  else {
    autofit()
    window.addEventListener('resize', autofit)

    // 字典数据
    dictApi().then((res) => {
      if (res.code === 200) {
        dict.value = res.data.data.map((item: any) => {
          switch (item.type) {
            case 2:
            case 3:
              item.value = JSON.parse(item.value)
              break
          }
          return item
        })
      }
    })

    // 专家数据
    expertApi().then((res) => {
      if (res.code === 200) {
        expertList.value = res.data.data.map((item: any) => {
          item.profilePhoto = item.profilePhoto ? `data:image/webp;base64,${item.profilePhoto}` : ''
          return item
        })
      }
    })

    // 轮询数据
    getData()
    setInterval(() => {
      getData()
    }, 1000 * 15)
  }
})
const homeCenterVisible = ref(false)
const homeExpertVisible = ref(false)

// 首页中间显隐
emitter.on('homeCenterVisible', (val: boolean) => {
  homeCenterVisible.value = val
  homeExpertVisible.value = !val
})

// 首页专家显隐
emitter.on('homeExpertTab', (val: number) => {
  homeExpertVisible.value = true
  homeCenterVisible.value = false
})

// 左右悬浮显隐
const leftRightVisible = ref(false)
emitter.on('leftRightVisible', (val: boolean) => {
  leftRightVisible.value = val
})

// 当前页面
const current = ref(0)
watch(
  () => current.value,
  (nval) => {
    emitter.emit('current', nval)
    if (nval !== 0) {
      homeCenterVisible.value = false
      homeExpertVisible.value = false
    }
  },
)
</script>

<template>
  <div class="relative h-full w-full overflow-hidden">
    <Model v-if="dict" :data="getDict('model_')" />
    <Header v-if="dict" v-model:current="current" />

    <!-- 产业概览 -->
    <Transition name="left">
      <HomeLeft v-if="current === 0 && dict && expertList" style="--index: 1" :data="Object.assign(getDict('home_left_'), { expert: expertList })" />
    </Transition>
    <Transition name="right">
      <HomeRight v-if="current === 0 && dict" style="--index: 1" :data="getDict('home_right_')" />
    </Transition>
    <Transition name="bottom">
      <HomeCenter v-if="current === 0 && homeCenterVisible && dict" v-model:visible="homeCenterVisible" style="--index: 1" :data="getDict('home_center_')" />
    </Transition>
    <Transition name="bottom">
      <HomeExpert v-if="current === 0 && homeExpertVisible && expertList" v-model:visible="homeExpertVisible" style="--index: 1" :data="expertList" />
    </Transition>

    <!-- 农业物联 -->
    <Transition name="left">
      <FarmLeft v-if="current === 1 && leftRightVisible && dict && deviceList" style="--index: 1" :data="getDict('farm_left_')" :device="deviceList.filter((item: any) => item.areaId === 1)" />
    </Transition>
    <Transition name="right">
      <FarmRight v-if="current === 1 && leftRightVisible && deviceList" style="--index: 1" :data="deviceList.filter((item: any) => item.deviceImei === '40341836')[0]" />
    </Transition>

    <!-- 工业物联 -->
    <Transition name="left">
      <FactoryLeft v-if="current === 2 && leftRightVisible && dict && deviceList" style="--index: 1" :data="getDict('factory_left_')" :device="deviceList.filter((item: any) => [2, 4, 5].includes(item.areaId))" />
    </Transition>
    <Transition name="right">
      <FactoryRight v-if="current === 2 && leftRightVisible && deviceList" style="--index: 1" :data="deviceList.filter((item: any) => ['860065074635402', '860065074633704', '860065074668932'].includes(item.deviceImei))" />
    </Transition>

    <!-- 监控云台 -->
    <Transition name="left">
      <CameraLeft v-if="current === 3" style="--index: 1" />
    </Transition>
    <Transition name="right">
      <CameraRight v-if="current === 3" style="--index: 1" />
    </Transition>
  </div>
</template>

<style lang="scss">
@import '@/styles/transition.scss';
.bottom-enter-active {
  transition-delay: 0s;
}
.box {
  position: absolute;
  z-index: 99;
  background: rgba(0, 47, 32, 0.8);
  box-shadow: 0 0 8rem rgba(89, 238, 193, 0.3);
  padding: 20rem;
  letter-spacing: 1rem;
  .card {
    .hd {
      position: relative;
      font-weight: bold;
      font-size: 24rem;
      font-family: AlimamaShuHeiTi;
      color: #fff;
      height: 58rem;
      padding-bottom: 4rem;
      text-shadow: 0 0 8rem rgba(89, 238, 193, 0.4);
      background: url(@/assets/images/<EMAIL>) no-repeat 52rem top;
      background-size: calc(100% - 52rem - 69rem) 58rem;
      padding-left: 15rem;
      &::before,
      &::after {
        content: '';
        position: absolute;
        z-index: -1;
        top: 0;
        height: 100%;
      }
      &::before {
        left: 0;
        width: 52rem;
        background: url(@/assets/images/<EMAIL>) no-repeat right top;
        background-size: 52rem 58rem;
      }
      &::after {
        right: 0;
        width: 69rem;
        background: url(@/assets/images/<EMAIL>) no-repeat right top;
        background-size: 69rem 58rem;
      }
      img {
        width: 22rem;
        height: 22rem;
      }
    }
    .bd {
      min-height: 150rem;
      color: #fff;
    }
  }
  &.factory {
    background: rgba(0, 47, 32, 0.8);
    box-shadow: 0 0 8rem rgba(32, 180, 187, 0.2);
    .card {
      .hd {
        text-shadow: 0 0 8rem rgba(32, 180, 187, 0.4);
        background: url(@/assets/images/<EMAIL>) no-repeat 52rem top;
        background-size: calc(100% - 52rem - 69rem) 58rem;
        &::before {
          background: url(@/assets/images/<EMAIL>) no-repeat right top;
          background-size: 52rem 58rem;
        }
        &::after {
          background: url(@/assets/images/<EMAIL>) no-repeat right top;
          background-size: 69rem 58rem;
        }
      }
    }
  }
}
</style>
