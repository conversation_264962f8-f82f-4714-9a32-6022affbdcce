<script setup lang="ts">
import { warnListApi } from '@/service'
import Air from './components/air.vue'
import Liveness from './components/liveness.vue'

defineProps<{
  data: any
}>()

const warnList = ref<any>([])

async function getData() {
  const res = await warnListApi()
  if (res.code === 200) {
    warnList.value = res.data.data
  }
}

const currentAir = ref(0)
onMounted(async () => {
  setInterval(() => {
    currentAir.value = (currentAir.value + 1) % 3
  }, 8000)

  await getData()
  setInterval(async () => await getData(), 1000 * 3600)
})

const currentHz = ref(1)
</script>

<template>
  <div class="box factory">
    <!-- card1 -->
    <div class="card">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">车间环境</span>
      </div>
      <div class="bd m-t-20rem">
        <NTabs v-model:value="currentAir" animated>
          <NTabPane :name="0" tab="普益茶">
            <Air :data="data.filter((item: any) => item.deviceImei === '860065074635402')[0].lastData" />
          </NTabPane>
          <NTabPane :name="1" tab="茧丝绸">
            <Air :data="data.filter((item: any) => item.deviceImei === '860065074633704')[0].lastData" />
          </NTabPane>
          <NTabPane :name="2" tab="富莎厂">
            <Air :data="data.filter((item: any) => item.deviceImei === '860065074668932')[0].lastData" />
          </NTabPane>
        </NTabs>
      </div>
    </div>
    <!-- card2 -->
    <div class="card m-t-10rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">设备振动监测</span>
      </div>
      <div class="bd m-t-20rem">
        <NTabs v-model:value="currentHz" animated>
          <NTabPane :name="0" tab="茧丝绸">
            <Liveness :id="2" />
          </NTabPane>
          <NTabPane :name="1" tab="富莎厂">
            <Liveness :id="5" />
          </NTabPane>
        </NTabs>
      </div>
    </div>
    <!-- card3 -->
    <div class="card m-t-10rem">
      <div class="hd flex items-center">
        <img src="@/assets/images/<EMAIL>">
        <span class="m-l-25rem">生产预警</span>
      </div>
      <div class="bd warning m-t-10rem">
        <div class="flex items-center justify-between p-y-5rem text-16rem font-bold">
          <div class="flex-1 p-l-16rem">
            位置
          </div>
          <div class="flex-1">
            设备名称
          </div>
          <div class="flex-1 p-r-16rem text-right">
            预警内容
          </div>
        </div>
        <NCarousel
          class="h-150rem w-100%"
          direction="vertical"
          :slides-per-view="4"
          :show-dots="false"
          autoplay
          draggable
        >
          <div v-for="(item, index) in warnList" :key="index" class="item h-100% flex items-center justify-between text-14rem opacity-80">
            <div class="flex-1 p-l-16rem">
              {{ item.device.areaName }}
            </div>
            <div class="flex-1">
              {{ item.device.deviceSpecific }}{{ item.device.deviceName }}
            </div>
            <div class="flex-1 p-r-16rem text-right">
              <NEllipsis :line-clamp="1" :tooltip="true">
                {{ item.warnDesc }}
              </NEllipsis>
            </div>
          </div>
        </NCarousel>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: 464rem;
  height: 936rem;
  right: 20rem;
  bottom: 20rem;
}

:deep(.n-tabs) {
  .n-tabs-tab-pad,
  .n-tabs-bar {
    display: none;
  }
  .n-tabs-nav-scroll-content {
    justify-content: center;
  }
  .n-tabs-wrapper {
    border: 1rem solid rgba(90, 246, 253, 0.26);
    border-radius: 4rem;
    .n-tabs-tab {
      font-size: 14rem;
      padding: 8rem 20rem 6rem;
      line-height: 1;
      color: rgba(255, 255, 255, 0.8);
      border: 1rem solid rgba(37, 220, 167, 0);
    }
    .n-tabs-tab--active {
      color: rgba(90, 246, 253, 1);
      background: rgba(37, 212, 220, 0.16);
      box-shadow: inset 0 0 5rem 0 #25d4dc;
      border: 1rem solid rgba(37, 212, 220, 0.8);
    }
    .n-tabs-tab-wrapper:nth-child(2) .n-tabs-tab--active {
      border-radius: 4rem 0 0 4rem;
    }
    .n-tabs-tab-wrapper:nth-child(4) .n-tabs-tab--active {
      border-radius: 0 4rem 4rem 0;
    }
  }
}
.warning {
  .item {
    line-height: 1;
  }
  .n-carousel__slide:nth-child(2n) {
    .item {
      background: rgba(90, 246, 253, 0.08);
    }
  }
}
</style>
