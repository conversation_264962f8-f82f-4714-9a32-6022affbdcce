import arrow from '@/assets/texture/arrow.png'
import chinaBlurLine from '@/assets/texture/chinaBlurLine.png'
import flyLineFocus from '@/assets/texture/flyLine.png'
import mapFlyline from '@/assets/texture/flyLine2.png'
import guangquan1 from '@/assets/texture/guangquan01.png'
import guangquan2 from '@/assets/texture/guangquan02.png'
import huiguang from '@/assets/texture/huiguang.png'
import ocean from '@/assets/texture/ocean-green-bg.png'
import pathLine1 from '@/assets/texture/pathLine1.png'
import pathLine2 from '@/assets/texture/pathLine2.png'
import pathLine3 from '@/assets/texture/pathLine3.png'
import point from '@/assets/texture/point.png'
import rotationBorder1 from '@/assets/texture/rotationBorder1.png'
import rotationBorder2 from '@/assets/texture/rotationBorder2.png'
import side from '@/assets/texture/side.png'
import { Resource } from 'mini3d'
import { FileLoader } from 'three'

export class Assets {
  constructor() {
    this.init()
  }

  init() {
    this.instance = new Resource()
    // 添加Fileloader
    this.instance.addLoader(FileLoader, 'FileLoader')

    // 资源加载
    const base_url = import.meta.env.VITE_BASE_URL
    const assets = [
      { type: 'Texture', name: 'pathLine1', path: pathLine1 },
      { type: 'Texture', name: 'pathLine2', path: pathLine2 },
      { type: 'Texture', name: 'pathLine3', path: pathLine3 },
      { type: 'Texture', name: 'huiguang', path: huiguang },
      { type: 'Texture', name: 'rotationBorder1', path: rotationBorder1 },
      { type: 'Texture', name: 'rotationBorder2', path: rotationBorder2 },
      { type: 'Texture', name: 'guangquan1', path: guangquan1 },
      { type: 'Texture', name: 'guangquan2', path: guangquan2 },
      { type: 'Texture', name: 'chinaBlurLine', path: chinaBlurLine },
      { type: 'Texture', name: 'ocean', path: ocean },
      { type: 'Texture', name: 'side', path: side },
      { type: 'Texture', name: 'flyLineFocus', path: flyLineFocus },
      { type: 'Texture', name: 'mapFlyline', path: mapFlyline },
      { type: 'Texture', name: 'arrow', path: arrow },
      { type: 'Texture', name: 'point', path: point },
      { type: 'Texture', name: 'material', path: `${base_url}model/material.jpg` },
      { type: 'Texture', name: 'water', path: `${base_url}model/waternormals.jpg` },
      { type: 'File', name: 'china', path: `${base_url}model/geo/dongtai.json` },
      { type: 'File', name: 'mapJson', path: `${base_url}model/geo/fuan.json` },
      { type: 'GLTF', name: 'farm', path: `${base_url}model/Farm.glb` },
      { type: 'GLTF', name: 'factoryA', path: `${base_url}model/A.glb` },
      { type: 'GLTF', name: 'factoryB', path: `${base_url}model/B.glb` },
      { type: 'GLTF', name: 'factoryC', path: `${base_url}model/C.glb` },
    ]
    // 资源加载
    this.instance.loadAll(assets)
  }
}
