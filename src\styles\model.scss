.model {
  position: relative;
  width: 100%;
  height: 100%;
  background: #0b2e24;
  margin: 0 auto;
  font-size: 14rem;
  .world {
    position: absolute;
    top: 100vh;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    &.show {
      top: 0;
      z-index: 1;
      opacity: 2;
    }
    .control {
      position: absolute;
      z-index: 9;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      bottom: 20rem;
      left: 0;
      .btn {
        width: 168rem;
        height: 57rem;
        line-height: 57rem;
        background: url(@/assets/images/<EMAIL>) no-repeat center;
        background-size: 149rem 49rem;
        font-size: 18rem;
        text-align: center;
        color: rgba(195, 255, 237, 0.8);
        margin: 0 5rem;
        cursor: pointer;
        pointer-events: all;
        transition: all 0.3s;
        user-select: none;
        transform: translateY(200%);
        &.active {
          background: url(@/assets/images/<EMAIL>) no-repeat center;
          background-size: 168rem 57rem;
          color: #feeaca;
        }
      }
    }
  }
}
